export type SerialportType = {
  // 功率显示
  power?: number
  // 周期
  period?: number
  // 占空比
  dutyRatio?: number
  // 温度T1
  temperatureOne?: string
  // 温度T2
  temperatureTwo?: string
  // 温度T1是否选择
  temperatureOneSelected?: number
  // 温度T2是否选择
  temperatureTwoSelected?: number
  // 音量
  volume?: number
  // 处方号
  prescriptionId?: number
  // 是否静音
  mute?: number
  // 是否锁屏
  lockscreen?: number
  // 实时功率
  realtimePower?: number
  // 辐射器
  radiator?: number
  // 辐射器是否选中
  radiatorSelected?: number
  // 测温传感器故障
  sensorFault?: number
  // 测温通讯故障
  communicationFault?: number
  // 倒计时
  countdown?: string
  // 指示灯
  light?: number

  // 新增指令数据
  // 温度T3
  temperatureThree?: string
  // 温度T4
  temperatureFour?: string
  // 温度T3是否选择
  temperatureThreeSelected?: number
  // 温度T4是否选择
  temperatureFourSelected?: number
  // 设定温度显示
  settingTemperature?: string
}

export type DrawFunc = () => void

export type GenerateDataFunc = () => [number, number][]
