import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  ManyToOne,
  JoinColumn
} from 'typeorm'
import Patient from './patient.entity'

@Entity({ name: 'patient_case' }) // 实体
export default class PatientCase {
  @PrimaryGeneratedColumn() // 自增主键
  id: number

  @Column({ type: 'varchar', nullable: true }) // 功率曲线数据
  chart: string // js 数据类型

  @Column({ name: 'cure_params', type: 'varchar', nullable: true }) // 治疗参数
  cureParams: string // js 数据类型

  @Column({ type: 'varchar' }) // 姓名
  detail: string // js 数据类型

  @Column({ type: 'varchar', nullable: true })
  hospitalName?: string

  @ManyToOne(() => Patient, patient => patient.cases)
  @JoinColumn({ name: 'patient_id' })
  patient: Patient

  @Column({ name: 'creator_id', type: 'int', nullable: true })
  creatorId?: number

  @Column({ type: 'varchar', nullable: true })
  creator?: string

  @CreateDateColumn({
    type: 'datetime',
    name: 'created_time',
    comment: '创建时间'
  })
  createdTime: Date

  @UpdateDateColumn({
    type: 'datetime',
    name: 'updated_time',
    comment: '更新时间'
  })
  updateTime: Date

  // 是否删除，逻辑删除
  @DeleteDateColumn({
    type: 'datetime',
    name: 'deleted_time',
    nullable: true,
    comment: '删除时间'
  })
  deletedTime: Date
}
