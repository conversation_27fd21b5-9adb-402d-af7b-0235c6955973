import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import electron from 'vite-plugin-electron'
import pkg from './package.json' assert { type: 'json' }

export default defineConfig({
  plugins: [
    vue(),
    electron([
      {
        entry: 'electron/main/index.ts',
        onstart(options) {
          options.startup()
        },
        vite: {
          build: {
            sourcemap: true,
            minify: process.env.NODE_ENV === 'production',
            outDir: 'dist-electron/main',
            rollupOptions: {
              external: Object.keys(
                require('./package.json').dependencies || {}
              )
            }
          }
        }
      },
      {
        entry: 'electron/preload/index.ts',
        onstart(options) {
          options.reload()
        },
        vite: {
          build: {
            sourcemap: 'inline',
            minify: process.env.NODE_ENV === 'production',
            outDir: 'dist-electron/preload',
            rollupOptions: {
              external: Object.keys(
                require('./package.json').dependencies || {}
              )
            }
          }
        }
      }
    ])
  ],
  server: process.env.VITE_DEV_SERVER_URL
    ? undefined
    : {
        host: pkg.env?.VITE_DEV_SERVER_HOST,
        port: pkg.env?.VITE_DEV_SERVER_PORT
      },
  clearScreen: false
})
