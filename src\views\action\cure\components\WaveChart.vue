<script setup lang="ts">
import { ref, unref, onMounted, watch } from 'vue';
import echarts from '@/utils/echarts'

const props = defineProps({
  data: {
    type: Array,
    required: true
  },
  xAxisMax: {
    type: Number,
    required: true,
    default: 1000
  },
  yAxisMax: {
    type: Number,
    required: true,
    default: 5
  }
})

const chartRef = ref<HTMLDivElement | null>(null)
let chartInstance: echarts.ECharts | null = null

onMounted(() => {
  const el = unref(chartRef)
  if (!el || !unref(el)) {
    return
  }

  chartInstance = echarts.init(el)

  const option: echarts.EChartsCoreOption = {
    animation: false,
    grid: {
      top: 40,
      left: 50,
      right: 70,
      bottom: 50
    },
    xAxis: {
      name: 'S',
      type: 'value',
      min: 0,
      max: props.xAxisMax,
      axisLine: {
        lineStyle: {
          color: '#333'
        },
        symbol: ['none', 'arrow'],
        symbolSize: [7, 15],
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        show: false
      },
      splitLine: {
        show: false
      }
    },
    yAxis: {
      name: 'W',
      max: props.yAxisMax,
      min: 0,
      axisLine: {
        lineStyle: {
          color: '#333'
        },
        symbol: ['none', 'arrow'],
        symbolSize: [7, 15],
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        show: false
      },
      splitLine: {
        show: false
      }
    },
    series: [
      {
        type: 'line',
        showSymbol: false,
        clip: true,
        data: [],
        lineStyle: {
          color: '#5470c6'
          // color: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc']
        }
      }
    ]
  }

  chartInstance.setOption(option)
})

watch(() => props.data, data => {
  // console.log('watch wave chart data : ', data)
  if (data) {
    chartInstance?.setOption({
      series: [
        {
          data
        }
      ]
    })
  }
})
</script>

<template>
  <div ref="chartRef"></div>
</template>
