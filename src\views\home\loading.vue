<script lang="ts" setup>
import PageWrapper from '@/components/PageWrapper/index.vue'
import { useUserStore } from '@/store/modules/user';
import { onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'

defineOptions({
  name: 'HomeLoadingPage'
})

const router = useRouter()
const userStore = useUserStore()

function goHomePage() {
  if (userStore.getLoginName) {
    setTimeout(() => router.push('/home'), 3000)
  } else {
    setTimeout(() => router.push('/login'), 3000)
  }
}

const leftClass = ref(true)

onMounted(() => {
  setTimeout(() => {
    leftClass.value = false
    goHomePage()
  }, 200)
})
</script>

<template>
  <PageWrapper show-logo :show-logout="false">
    <div class="content-wrapper py-28 flex-center relative">
      <div class="absolute overflow-hidden top-36 w-[700px] h-12">
        <p class="text-5xl text-center w-full text-white absolute title" :class="{ left: leftClass }">欢迎使用江苏诺万医疗产品</p>
      </div>
      <span class="text-primary text-5xl">启 动 中... ...</span>
    </div>
  </PageWrapper>
</template>
<style scoped>
.title {
  transition-property: left;
  transition-duration: 2500ms;
  transition-delay: 0ms;
  transition-timing-function: linear;
  left: 0;
}

.title.left {
  left: 650px;
}
</style>
