<template>
  <PageWrapper :showLogout="false" :logoutAutoBack="false" @logout="handleLogout">
    <div class="content-wrapper pt-28 pb-24 col-flex justify-between items-center relative">
      <p class="text-5xl text-center">{{ basicInfo.hospitalName }}</p>
      <div class="flex-center mt-16"><img class="w-56 home-logo" src="@/assets/img/logo-yellow.png"></div>
      <div class="col-flex items-center pt-16">
        <p class="text-5xl text-center">{{ basicInfo.instrumentModel }}</p>
        <div class="py-20 flex-center justify-between w-full">
          <button class="menu-item w-48 mr-12" @click="handleIntro">介绍</button>
          <button class="menu-item w-48" @click="handleAction">操作</button>
        </div>
      </div>
      <div class="flex-center">
        <div class="text-base font-bold w-58">
          <p class="text-lg mb-2">江苏诺万医疗设备有限公司</p>
          <p>
            <span class="inline-flex w-28 justify-between">
              <span>销</span><span>售</span><span>热</span><span>线：</span>
            </span>
            <span class="ml-1">0516-87980008</span>
          </p>
          <p>
            <span class="inline-flex w-28 justify-between">
              <span>售</span><span>后</span><span>热</span><span>线：</span>
            </span>
            <span class="ml-1">0516-87980000</span>
          </p>
          <p>
            <span class="inline-flex w-28 justify-between">
              <span>网</span><span>址：</span>
            </span>
            <span class="ml-1">www.nuowan.com</span>
          </p>
          <p class="w-full flex">
            <span class="inline-flex w-54 justify-between items-center">
              <span class="flex-1 break-all">
                <template v-if="touchScreenVersionNameArr.length">
                  <template v-for="item in touchScreenVersionNameArr">
                    <span>{{ item }}</span>
                  </template>
                </template>
              </span>
              <span>：</span>
            </span>
            <span class="inline-flex flex-1 items-center ml-1">{{ formatedTouchScreenVersion }}</span>
          </p>
          <!-- <p>
            <span class="inline-flex w-16 justify-between">
              <span>主</span><span>控</span><span>板</span>
            </span>：{{ mainControlBoardVersion }}
          </p> -->
        </div>
      </div>
    </div>
    <Modal :visible="infoModalVisible" :width="500" wrapClass="bg-white">
      <template #default>
        <div class="px-4 pt-12 pb-4">
          <div class="flex items-center mb-4">
            <div class="w-32 text-right mr-4">仪器型号:</div>
            <div class="flex-1">
              <el-input v-model="form.instrumentModel" type="text" size="large" />
            </div>
          </div>
          <div class="flex items-center mb-4">
            <div class="w-32 text-right mr-4">医院名称:</div>
            <div class="flex-1">
              <el-input v-model="form.hospitalName" type="text" size="large" />
            </div>
          </div>
          <div class="flex items-center mb-4">
            <div class="w-32 text-right mr-4">版本:</div>
            <div class="flex-1">
              <el-input v-model="form.touchScreenVersion" type="text" size="large" maxlength="12" show-word-limit />
            </div>
          </div>
          <div class="flex items-center mb-4">
            <div class="w-32 text-right mr-4">版本显示名称:</div>
            <div class="flex-1">
              <el-input v-model="form.touchScreenVersionName" type="text" size="large" maxlength="12" show-word-limit />
            </div>
          </div>
        </div>
      </template>
      <template #footer>
        <div class="p-4 flex justify-end">
          <div class="btn btn-active btn-ghost mr-4" @click="infoModalVisible = false">取消</div>
          <div class="btn btn-primary" @click="handleUpdateBaseInfo">确定</div>
        </div>
      </template>
    </Modal>
    <div class="absolute w-36 h-36 right-0 top-0" @click="onTopRightClick"></div>
    <div class="absolute w-36 h-36 left-0 top-0" @click="onTopLeftClick"></div>
    <div class="absolute w-36 h-36 left-0 bottom-0 z-50" @click="onBottomLeftClick"></div>
    <div class="absolute bottom-20 left-20 flex items-center text-2xl">
      <span v-if="switched" class="rounded-2xl w-6 h-6 bg-red-500 mr-3"></span>
      <span v-else class="rounded-2xl w-6 h-6 bg-green-500 mr-3"></span>

      <span v-if="switched">未连接</span>
      <span v-else>已连接</span>
    </div>
    <div class="absolute bottom-20 right-20 flex items-center text-2xl">
      <el-dropdown placement="top" @command="handleCommand">
        <el-button :icon="User"> {{ userStore.getLoginName }} </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="change-password">修改密码</el-dropdown-item>
            <el-dropdown-item v-if="userStore.getUserRole !== UserRole.USER"
              command="user-management">用户管理</el-dropdown-item>
            <el-dropdown-item command="logout">退出登录</el-dropdown-item>
            <el-dropdown-item command="quit-app">退出程序</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
    <ChangePassword ref="changePasswordRef" />
  </PageWrapper>
</template>

<script setup lang="ts">
import { onMounted, ref, unref, onUnmounted, watch, reactive, onBeforeUnmount, computed } from 'vue'
import { useRouter } from 'vue-router';
import PageWrapper from '@/components/PageWrapper/index.vue'
import Modal from '@/components/Modal/index.vue'
import { useSerialportStore } from '@/store/modules/serialport'
import { useUserStore } from '@/store/modules/user'
import { User } from '@element-plus/icons-vue'
import { UserRole } from '@/types/entity'
import ChangePassword from './ChangePassword.vue';

type IBaseInfo = {
  instrumentModel: string
  hospitalName: string
  touchScreenVersion: string
  touchScreenVersionName: string
}

defineOptions({
  name: 'HomePage'
})

const changePasswordRef = ref()
const userStore = useUserStore()
function handleCommand(command: string) {
  console.log('command : ', command)
  switch (command) {
    case 'change-password':
      changePasswordRef.value?.show?.()
      break
    case 'user-management':
      goPage('/user/management')
      break
    case 'logout':
      userStore.logout()
      router.replace('/login')
      break
    case 'quit-app':
      window.electronAPI.serialport.quitApp()
      break
  }
}

const router = useRouter()
const serialportStore = useSerialportStore()
const mainControlBoardVersion = ref('')

const switched = computed(() => {
  return serialportStore.getSwitched
})
watch(() => serialportStore.getSwitched, data => {
  console.log('switched change : ', data)
  if (data === true) {
    mainControlBoardVersion.value = ''
  }
}, {
  immediate: true
})

function goPage(path: string) {
  router.push({ path })
}

const infoModalVisible = ref<boolean>(false)

const basicInfo = ref<IBaseInfo>({
  instrumentModel: '',
  hospitalName: '',
  touchScreenVersion: '',
  touchScreenVersionName: ''
})

const formatedTouchScreenVersion = computed(() => {
  if (basicInfo.value.touchScreenVersion?.length) {
    const versionItems = basicInfo.value.touchScreenVersion.split('.')
    return versionItems[0]
  }

  return ''
})

const touchScreenVersionNameArr = computed(() => {
  if (basicInfo.value.touchScreenVersionName && basicInfo.value.touchScreenVersionName.length > 0) {
    return basicInfo.value.touchScreenVersionName.split('')
  }

  return []
})

const form = ref<IBaseInfo>({
  instrumentModel: '',
  hospitalName: '',
  touchScreenVersion: '',
  touchScreenVersionName: ''
})

onMounted(() => {
  window.electronAPI.sqlite.getBasicInfo().then(res => {
    console.log('get basic info result : ', res)
    if (res) {
      basicInfo.value.instrumentModel = res.instrumentModel
      basicInfo.value.hospitalName = res.hospitalName
      basicInfo.value.touchScreenVersion = res.touchScreenVersion
      basicInfo.value.touchScreenVersionName = res.touchScreenVersionName

      form.value.instrumentModel = res.instrumentModel
      form.value.hospitalName = res.hospitalName
      form.value.touchScreenVersion = res.touchScreenVersion
      form.value.touchScreenVersionName = res.touchScreenVersionName

      userStore.setCasePath(res.casePath)
      userStore.setHospitalName(res.hospitalName)
    }
  })

  window.electronAPI.serialport.dispatch('CurrentPage.ActionChoose')
  window.electronAPI.serialport.dispatch('StartingUp.ResetVersion')
  // action-choose-page
  window.electronAPI.serialport.receive('action-choose-page', (_, { version }: { version: string }) => {
    console.log('main board version : ', version)
    if (version) {
      serialportStore.setSwitched(false)
    }
    if (version && version !== mainControlBoardVersion.value) {
      mainControlBoardVersion.value = version
    }
  })

  // 绑定回车输入事件
  keydownEvent()
})

onUnmounted(() => {
  window.electronAPI.serialport.removeListener('action-choose-page')
})

function handleIntro() {
  if (switched.value) {
    ElMessage({
      showClose: false,
      message: '连接断开',
      type: 'warning',
      grouping: true
    })
    return
  }
  goPage('/introduction/menu')
}

function handleAction() {
  goPage('/patient-case/management')
}

function onChangeBaseInfo() {
  form.value.hospitalName = basicInfo.value.hospitalName
  form.value.instrumentModel = basicInfo.value.instrumentModel
  form.value.touchScreenVersion = basicInfo.value.touchScreenVersion

  infoModalVisible.value = true
}

async function handleUpdateBaseInfo() {
  console.log('base info : ', unref(form.value))
  const parmas = {
    ...form.value
  }
  // @ts-ignore
  window.electronAPI.sqlite.updateBasicInfo(parmas).then(res => {
    // console.log('update basic info res : ', res)
    if (res) {
      infoModalVisible.value = false
      form.value.instrumentModel = res.instrumentModel
      form.value.hospitalName = res.hospitalName
      form.value.touchScreenVersion = res.touchScreenVersion
      form.value.touchScreenVersionName = res.touchScreenVersionName
      basicInfo.value.instrumentModel = res.instrumentModel
      basicInfo.value.hospitalName = res.hospitalName
      basicInfo.value.touchScreenVersion = res.touchScreenVersion
      basicInfo.value.touchScreenVersionName = res.touchScreenVersionName

      userStore.setCasePath(res.casePath)
      userStore.setHospitalName(res.hospitalName)

      ElMessage({
        showClose: false,
        message: '保存成功',
        type: 'success',
        grouping: true
      })
    }
  })
}

function handleLogout() {
  console.log('handle logout ~~')
  window.electronAPI.serialport.quitApp()
}

type IClickState = {
  one: boolean
  two: boolean
  three: boolean
}

const clickState = reactive<IClickState>({ one: false, two: false, three: false })

function onTopRightClick() {
  console.log('右上角点击了：', clickState)
  if (!clickState.one) clickState.one = true
  if (clickState.two) clickState.two = false
  if (clickState.three) clickState.three = false
  console.log('右上角状态变了：', clickState)
}

function onTopLeftClick() {
  console.log('左上角点击了：', clickState)
  if (clickState.one && !clickState.three) clickState.two = true
  console.log('左上角状态变了：', clickState)
}

function onBottomLeftClick() {
  console.log('左下角点击了：', clickState)
  if (clickState.one && clickState.two) clickState.three = true
  console.log('左下角状态变了：', clickState)
}

watch(clickState, (newState) => {
  if (newState.one && newState.two && newState.three) {
    if (userStore.getUserRole !== UserRole.USER) {
      onChangeBaseInfo()
    }
  }
})

onBeforeUnmount(() => {
  clickState.one = false
  clickState.two = false
  clickState.three = false
})

function keydownEvent() {
  document.onkeydown = (e: any) => {
    if (e.defaultPrevented) {
      return;
    }

    if (e.keyCode === 13 && infoModalVisible.value) {
      handleUpdateBaseInfo()
    }
  }
}
</script>
<style scoped>
.ctenhd {
  width: 192px;
  height: 48px;
  line-height: 48px;
  border-radius: 4px;
  color: #fff;
  /* background: url(@/assets/img/listback.png);
  background-size: 100% 100%; */
  text-align: center;
  font-weight: 700;
  font-size: 22px;
  cursor: pointer;
  /* text-shadow: 10px 0 15px #0CB2B9, 0 10px 15px #0CB2B9,
    0 -10px 15px #0CB2B9, -10px 0 15px #0CB2B9; */
  /* margin-right: 75px; */
}

.ctenhd.w-56 {
  width: 300px;
  margin-right: 0;
}

.codkd {
  border-radius: 5px;
  padding: 5px 15px;
  color: #000;
  background: #fff;
  border: solid 1px #bbb;
}
</style>
