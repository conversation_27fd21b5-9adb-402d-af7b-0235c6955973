<template>
  <div class="page-wrapper">
    <slot />
    <div v-if="props.showLogo" class="logo-wrapper select-none">
      <img class="h-[60px] wrapper-logo" src="@/assets/img/logo-yellow.png">
    </div>
    <div v-if="props.showLogout" class="exit_new bg-exit select-none" @click="handleLogout">{{ props.logoutText }}</div>
  </div>
</template>

<script setup lang="ts">
import { withDefaults } from 'vue';
import { useRouter } from 'vue-router';

interface IPageWrapperProps {
  showLogo?: boolean
  showLogout?: boolean,
  logoutAutoBack?: boolean,
  logoutText?: string
}

const props = withDefaults(defineProps<IPageWrapperProps>(), { showLogo: false, showLogout: true, logoutAutoBack: true, logoutText: '退出' })

const emits = defineEmits(['logout'])

const router = useRouter()

function handleLogout() {
  if (props.logoutAutoBack) router.back()
  emits('logout')
}
</script>
<script lang="ts">
export default {
  name: 'PageWrapper'
}
</script>
<style scoped>
.logo-wrapper {
  position: absolute;
  bottom: 55px;
  left: 100px;
}

.exit_new {
  position: absolute;
  bottom: 55px;
  right: 80px;
  width: 120px;
  height: 48px;
  line-height: 48px;
  /* background-color: #046a80; */
  color: #000;
  border-radius: 4px;
  /* background: url(@/assets/img/buttonback.png);
  background-size: 100% 100%; */
  text-align: center;
  font-weight: 700;
  font-size: 18px;
  cursor: pointer;
}
</style>
