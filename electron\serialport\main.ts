import { BrowserWindow } from 'electron'
import { DelimiterParser } from '@serialport/parser-delimiter'
import { generateWriteData } from './write'
import { isEffectiveData } from './util'
import InstructionParser from './parser'
import { hexToNumber } from './util'

// TODO：真机调试用
import { SerialPort } from 'serialport'
// TODO：测试用
// import { SerialPortStream } from '@serialport/stream'
// import { MockBinding } from '@serialport/binding-mock'
// import { mockingReceiveData } from './mock'

import type { ElectronLog } from 'electron-log'
import type { Methods, ReceiveData, CurrentPage } from './types'

export type InitializeOptions = Readonly<{
  logger: ElectronLog
  window: BrowserWindow
}>

export class MainSerialPort {
  private isReady = false // 串口连接是否打开

  private logger?: ElectronLog // 日志

  private mainWindow: BrowserWindow // 主窗口实例

  // TODO: 真机调试
  private serialport: SerialPort
  // TODO：测试用
  // private serialport: SerialPortStream

  private delimiterParser: DelimiterParser

  private parsing = false

  private currentPage: CurrentPage

  private receiveData: ReceiveData

  private parser: InstructionParser

  private timer: NodeJS.Timeout

  private reconnectTimer: NodeJS.Timeout

  private latestDataTime: number = 0

  private switched: boolean = true

  private tempCalibrationTimer: NodeJS.Timeout

  constructor(window: BrowserWindow, logger?: ElectronLog) {
    this.mainWindow = window
    if (logger) this.logger = logger

    // TODO： 真机调试
    this.serialport = new SerialPort({
      path: 'COM4',
      // path: 'COM2',
      // path: 'COM1',
      baudRate: 115200,
      dataBits: 8,
      stopBits: 1,
      parity: 'none',
      autoOpen: false
    })

    // TODO： 测试用
    // MockBinding.createPort('/dev/ROBOT', { echo: true, record: true })
    // this.serialport = new SerialPortStream({
    //   binding: MockBinding,
    //   path: '/dev/ROBOT',
    //   baudRate: 14400,
    //   autoOpen: false
    // })

    this.delimiterParser = this.serialport.pipe(
      new DelimiterParser({ delimiter: [0x5a, 0xa5] })
    )

    this.parser = new InstructionParser()

    this.serialport.on('close', () => {
      this.isReady = false
      this.invokeRender('switch-touch-screen', {
        ready: this.isReady
      })

      // 检查窗口状态再记录日志
      if (!this.mainWindow || this.mainWindow.isDestroyed()) {
        console.log('serialport : port close (window destroyed)')
      } else {
        this.logger?.info('serialport : port close')
      }

      // 开启重连机制
      this.startReconnectLoop()
    })

    this.serialport.on('error', error => {
      if (!this.mainWindow || this.mainWindow.isDestroyed()) {
        console.error('serialport error (window destroyed):', error.message)
      } else {
        this.logger?.error(`serialport error: ${error.message}`)
      }
      this.isReady = false
      // 发生错误时也尝试重连
      this.startReconnectLoop()
    })
  }

  public init() {
    if (this.isReady) throw new Error('Already initialized')

    this.open()
  }

  private startReconnectLoop() {
    // 检查窗口是否还有效，如果窗口已销毁则不进行重连
    if (!this.mainWindow || this.mainWindow.isDestroyed()) {
      console.log('serialport : main window destroyed, stopping reconnect loop')
      return
    }

    this.logger?.info('serialport : port reconnect')
    clearTimeout(this.reconnectTimer)
    this.reconnectTimer = undefined
    if (!this.isReady) {
      this.reconnectTimer = setTimeout(() => {
        // 再次检查窗口状态
        if (!this.mainWindow || this.mainWindow.isDestroyed()) {
          console.log(
            'serialport : main window destroyed during reconnect, aborting'
          )
          return
        }
        try {
          this.init()
          this.startParseLoop()
        } catch (error) {
          console.error('serialport : reconnect failed', error)
        }
      }, 3000)
    }
  }

  private initReceiveData() {
    this.receiveData = {
      /******** 首页 ********/
      // 主控板版本
      mainboardPrefix: '',
      mainboardSuffix: '',

      /******** 波形界面 ********/
      // 功率显示
      power: '',
      // 周期
      period: '',
      // 占空比
      dutyRatio: '',
      // 温度T1显示
      temperatureOne: [],
      // 温度T1是否选择
      temperatureOneSelected: '',
      // 温度T2
      temperatureTwo: [],
      // 温度T2是否选择
      temperatureTwoSelected: '',
      // 音量
      volume: '',
      // 处方号
      prescriptionId: '',
      // 是否静音
      mute: '',
      // 是否锁屏
      lockscreen: '',
      // 实时功率
      realtimePower: '',
      // 辐射器
      radiator: '',
      // 辐射器是否选中
      radiatorSelected: '',
      // 测温传感器故障
      sensorFault: '',
      // 测温通讯故障
      communicationFault: '',
      // 倒计时
      countdown: '',
      // 指示灯
      light: '',
      // 波形界面新增指令
      // 温度T3显示
      temperatureThree: [],
      // 温度T3是否选择
      temperatureThreeSelected: '',
      // 温度T4
      temperatureFour: [],
      // 温度T4是否选择
      temperatureFourSelected: '',
      settingTemperature: '',

      // 校准界面
      // 功率标签值显示
      powerLabel: '',
      // 功率DA数据显示
      powerDA: '',
      // 功率校准切换步进
      powerStep: ''
    }
  }

  // 关闭串口的方法
  private open() {
    if (this.reconnectTimer) {
      clearInterval(this.reconnectTimer)
      this.reconnectTimer = undefined
    }

    if (this.serialport.isOpen) throw new Error('Serial Port is opened')

    if (this.serialport.opening) throw new Error('Serial Port is opening')

    this.serialport.open(err => {
      if (err) {
        // 检查窗口状态再记录日志
        if (!this.mainWindow || this.mainWindow.isDestroyed()) {
          console.error('serialport : port open failure (window destroyed)')
        } else {
          this.logger?.error('serialport : port open failure')
        }
        // 开启重连机制
        this.startReconnectLoop()
      } else {
        if (!this.mainWindow || this.mainWindow.isDestroyed()) {
          console.log('serialport : port open success (window destroyed)')
          return
        }
        this.logger?.info('serialport : port open success')
        this.isReady = true
        this.initReceiveData()
        this.logger?.info('send ready state : ', this.isReady)
        this.invokeRender('switch-touch-screen', {
          ready: this.isReady
        })

        // 绑定监听事件
        this.delimiterParser.on('data', this.dataListener.bind(this))

        // 启动解析定时器，每间隔0.8秒，解析0.2秒
        this.startParseLoop()
      }
    })
  }

  private startParseLoop() {
    // 开启前先清空timer
    if (this.timer) {
      clearTimeout(this.timer)
      this.timer = undefined
    }
    this.parsing = true
    this.timer = setTimeout(() => {
      this.parsing = false
      this.handleSwitch()
      if (this.timer) {
        clearTimeout(this.timer)
        this.timer = undefined
      }
      this.timer = setTimeout(() => {
        this.startParseLoop()
      }, 300)
    }, 200)
  }

  private handleSwitch() {
    if (Date.now() - this.latestDataTime > 60 * 1000) {
      // 超过15秒没收到数据，判断为已经切换到触摸屏
      if (!this.switched) {
        this.switched = true
        this.initReceiveData()
        this.invokeRender('switch-touch-screen', {
          switched: this.switched
        })
        this.logger.warn(
          `serialport : No data received for more than 15 seconds, it is judged as switching to the touch screen, switched status is ${this.switched}`
        )
      }
    } else {
      if (this.switched) {
        this.switched = false
        this.invokeRender('switch-touch-screen', {
          switched: this.switched
        })
        this.logger.warn(
          `serialport : Resume receiving data, switched status is ${this.switched}`
        )
      }
    }
  }

  // 关闭串口的方法
  public async close(): Promise<void> {
    if (!this.isReady) throw new Error('Not initialized')

    this.serialport.close(() => {
      this.logger.info('serialport: port is close')
    })
  }

  // 重置波形界面参数
  private resetWavePageData() {
    this.receiveData.mute = ''
    // this.receiveData.temperatureOne = []
    // this.receiveData.temperatureTwo = []
    this.receiveData.communicationFault = ''
    this.receiveData.countdown = ''
    this.receiveData.dutyRatio = ''
    this.receiveData.light = ''
    this.receiveData.lockscreen = ''
    this.receiveData.mute = ''
    this.receiveData.period = ''
    this.receiveData.power = ''
    this.receiveData.radiator = ''
    this.receiveData.radiatorSelected = ''
    this.receiveData.realtimePower = ''
    this.receiveData.sensorFault = ''
    this.receiveData.temperatureOneSelected = ''
    this.receiveData.temperatureTwoSelected = ''
    this.receiveData.temperatureThreeSelected = ''
    this.receiveData.temperatureFourSelected = ''
    this.receiveData.volume = ''
    this.receiveData.settingTemperature = ''
  }

  // 重置功率校准界面参数
  private resetPowerCalibrationData() {
    this.receiveData.powerLabel = ''
    this.receiveData.powerDA = ''
    this.receiveData.powerStep = ''
  }

  // 计算温度T1数组平均值
  private calcTempOneAvg() {
    return Math.round(
      this.receiveData.temperatureOne.reduce((p, n) => p + n, 0) /
        this.receiveData.temperatureOne.length
    )
  }

  // 计算温度T2数组平均值
  private calcTempTwoAvg() {
    return Math.round(
      this.receiveData.temperatureTwo.reduce((p, n) => p + n, 0) /
        this.receiveData.temperatureTwo.length
    )
  }

  // 计算温度T3数组平均值
  private calcTempThreeAvg() {
    return Math.round(
      this.receiveData.temperatureThree.reduce((p, n) => p + n, 0) /
        this.receiveData.temperatureThree.length
    )
  }

  // 计算温度T4数组平均值
  private calcTempFourAvg() {
    return Math.round(
      this.receiveData.temperatureFour.reduce((p, n) => p + n, 0) /
        this.receiveData.temperatureFour.length
    )
  }

  private sendTemperatureOne() {
    const avgOne = this.calcTempOneAvg()

    this.invokeRender('wave-page-display', {
      temperatureOne: `${avgOne}`
    })
    this.invokeRender('temperature-one-display', {
      temperatureOne: `${avgOne}`
    })
  }

  private sendTemperatureTwo() {
    const avgTwo = this.calcTempTwoAvg()

    this.invokeRender('wave-page-display', {
      temperatureTwo: `${avgTwo}`
    })
    this.invokeRender('temperature-two-display', {
      temperatureTwo: `${avgTwo}`
    })
  }

  private sendTemperatureThree() {
    const avgThree = this.calcTempThreeAvg()

    this.invokeRender('wave-page-display', {
      temperatureThree: `${avgThree}`
    })
    this.invokeRender('temperature-three-display', {
      temperatureThree: `${avgThree}`
    })
  }

  private sendTemperatureFour() {
    const avgFour = this.calcTempFourAvg()

    this.invokeRender('wave-page-display', {
      temperatureFour: `${avgFour}`
    })
    this.invokeRender('temperature-four-display', {
      temperatureFour: `${avgFour}`
    })
  }

  // 串口通信的所有操作都会通过这个方法通知子线程来执行
  public async serialportCall(method: Methods, args: any[]): Promise<void> {
    if (method === 'StartingUp.GetReadyStatus') {
      this.invokeRender('switch-touch-screen', {
        ready: this.isReady,
        switched: this.switched
      })
      this.logger.info(
        `serialport: send connect status, ready status is ${this.isReady}; switched status is ${this.switched}`
      )
    }
    if (!this.isReady) throw new Error('Not initialized')

    if (method === 'StartingUp.GetReadyStatus') {
      // nothing
    } else if (method === 'StartingUp.ResetVersion') {
      this.receiveData.mainboardPrefix = ''
      this.receiveData.mainboardSuffix = ''
    } else if (method === 'CurrentPage.ActionChoose') {
      this.currentPage = 'ActionChoose'
    } else if (method === 'CurrentPage.WavePage') {
      this.currentPage = 'WavePage'
      // 重置波形界面参数
      this.resetWavePageData()
      this.sendTemperatureOne()
      this.sendTemperatureTwo()
      this.sendTemperatureThree()
      this.sendTemperatureFour()
    } else if (method === 'CurrentPage.WavePage.RealtimePower') {
      this.currentPage = 'WavePage.RealtimePower'
      // 重置初始化
      this.resetWavePageData()
      this.sendTemperatureOne()
      this.sendTemperatureTwo()
      this.sendTemperatureThree()
      this.sendTemperatureFour()
    } else if (method === 'CurrentPage.TemperatureCalibrationPageOneSend') {
      this.currentPage = 'TemperatureCalibrationPageOneSend'
      this.sendTemperatureOne()
      this.sendTemperatureTwo()
      this.sendTemperatureThree()
      this.sendTemperatureFour()
    } else if (method === 'CurrentPage.TemperatureCalibrationPageTwoSend') {
      this.currentPage = 'TemperatureCalibrationPageTwoSend'
      this.sendTemperatureOne()
      this.sendTemperatureTwo()
      this.sendTemperatureThree()
      this.sendTemperatureFour()
    } else if (method === 'CurrentPage.TemperatureCalibrationPageThreeSend') {
      this.currentPage = 'TemperatureCalibrationPageThreeSend'
      this.sendTemperatureOne()
      this.sendTemperatureTwo()
      this.sendTemperatureThree()
      this.sendTemperatureFour()
    } else if (method === 'CurrentPage.TemperatureCalibrationPageFourSend') {
      this.currentPage = 'TemperatureCalibrationPageFourSend'
      this.sendTemperatureOne()
      this.sendTemperatureTwo()
      this.sendTemperatureThree()
      this.sendTemperatureFour()
    } else if (method === 'CurrentPage.PowerCalibrationPageOneSend') {
      this.currentPage = 'PowerCalibrationPageOneSend'
      this.resetPowerCalibrationData()
    } else {
      // const writeData: number[] | Buffer =
      //   method === 'TemperatureCalibrationPageOneSend.SaveExit' ||
      //   method === 'TemperatureCalibrationPageOneSend.NotSaveExit'
      //     ? Buffer.from(generateWriteData(method, ...args))
      //     : generateWriteData(method, ...args)
      const writeData: Buffer = Buffer.from(generateWriteData(method, ...args))
      // const writeData: number[] = generateWriteData(method, ...args)
      console.log('write data : ', writeData, method)
      this.serialport.write(writeData, error => {
        if (error) {
          this.logger.error(
            `serialport : port write error ${error.name} - ${error.message}`
          )
          if (error.stack) this.logger.error(`error stack : ${error.stack}`)

          return
        }

        // TODO: 测试环境下放开注释，模拟串口数据
        // mockingReceiveData(method, this.serialport)
      })
    }
  }

  private invokeRender(eventName: string, ...args: any[]) {
    // console.log('invoke render : ', eventName, args)
    try {
      // 检查窗口和webContents是否有效
      if (!this.mainWindow || this.mainWindow.isDestroyed()) {
        console.warn(
          `serial port : main window is destroyed, cannot send ${eventName}`
        )
        return
      }

      if (
        !this.mainWindow.webContents ||
        this.mainWindow.webContents.isDestroyed()
      ) {
        console.warn(
          `serial port : webContents is destroyed, cannot send ${eventName}`
        )
        return
      }

      this.mainWindow.webContents.send(eventName, ...args)
    } catch (error) {
      // 使用 console 而不是 logger 来避免循环问题
      console.warn(`serial port : invoke render error ${error}`)
    }
  }

  private dataListener(buffer: Buffer): void {
    // console.log('receive buffer : ', buffer.toString())
    this.latestDataTime = Date.now()
    if (this.switched) {
      this.switched = false
      this.invokeRender('switch-touch-screen', {
        switched: this.switched
      })
      this.logger.warn(
        `serialport : Resume receiving data, switched status is ${this.switched}`
      )
    }
    if (!isEffectiveData(buffer)) return

    // if (!this.parsing) return

    /***************** 全页面解析指令 - start *****************/

    // 页面查询指令
    const bufferStr = buffer.toString('hex')
    // console.log('receive buffer : ', bufferStr)
    if (bufferStr === '03810302') {
      this.invokeRender('send-page-no')
    }

    // T1 - T4温度校准页面退出指令
    if (bufferStr === '048003002d') {
      console.log('exit buffer : ', this.currentPage)
      if (this.currentPage === 'TemperatureCalibrationPageOneSend') {
        this.invokeRender('temperature-one-display', { exit: true })
      }

      if (this.currentPage === 'TemperatureCalibrationPageTwoSend') {
        this.invokeRender('temperature-two-display', { exit: true })
      }

      if (this.currentPage === 'TemperatureCalibrationPageThreeSend') {
        this.invokeRender('temperature-three-display', { exit: true })
      }

      if (this.currentPage === 'TemperatureCalibrationPageFourSend') {
        this.invokeRender('temperature-four-display', { exit: true })
      }
    }

    // 温度校准选择界面退出指令
    if (bufferStr === '0480030003') {
      this.invokeRender('temperature-calibration-choose-display', {
        exit: true
      })
    }

    // 温度T1、T2
    if (bufferStr.startsWith('07821011')) {
      const temperatureOne = this.parser.parseTemperature(
        bufferStr.substring(8, 12)
      )

      const temperatureTwo = this.parser.parseTemperature(
        bufferStr.substring(12, 16)
      )

      if (this.receiveData.temperatureOne.length > 3) {
        // 计算原数组平均值
        const oldAvg = this.calcTempOneAvg()

        // 删除首位
        this.receiveData.temperatureOne.shift()
        // 添加新值
        this.receiveData.temperatureOne.push(temperatureOne)
        // 计算新的平均值
        const newAvg = this.calcTempOneAvg()

        // 新老平均值不相等，则回调渲染进程
        if (['WavePage', 'WavePage.RealtimePower'].includes(this.currentPage)) {
          if (oldAvg !== newAvg) {
            this.invokeRender('wave-page-display', {
              temperatureOne: `${newAvg}`
            })
          }
        }
      } else {
        this.receiveData.temperatureOne.push(temperatureOne)

        if (['WavePage', 'WavePage.RealtimePower'].includes(this.currentPage)) {
          if (this.receiveData.temperatureOne.length > 3) {
            const avg = this.calcTempOneAvg()

            this.invokeRender('wave-page-display', {
              temperatureOne: `${avg}`
            })
          }
        }
      }

      if (this.receiveData.temperatureTwo.length > 3) {
        // 计算原数组平均值
        const oldAvg = this.calcTempTwoAvg()
        // 删除首位
        this.receiveData.temperatureTwo.shift()
        // 添加新值
        this.receiveData.temperatureTwo.push(temperatureTwo)
        // 计算新的平均值
        const newAvg = this.calcTempTwoAvg()
        // 新老平均值不相等，则回调渲染进程
        if (['WavePage', 'WavePage.RealtimePower'].includes(this.currentPage)) {
          if (oldAvg !== newAvg) {
            this.invokeRender('wave-page-display', {
              temperatureTwo: `${newAvg}`
            })
          }
        }
      } else {
        this.receiveData.temperatureTwo.push(temperatureTwo)

        if (['WavePage', 'WavePage.RealtimePower'].includes(this.currentPage)) {
          if (this.receiveData.temperatureTwo.length > 3) {
            const avg = this.calcTempTwoAvg()

            this.invokeRender('wave-page-display', {
              temperatureTwo: `${avg}`
            })
          }
        }
      }
    }

    /** 以下为新增温度T3，T4解析逻辑 */
    // 温度T3、T4
    if (bufferStr.startsWith('07824001')) {
      const temperatureThree = this.parser.parseTemperature(
        bufferStr.substring(8, 12)
      )

      const temperatureFour = this.parser.parseTemperature(
        bufferStr.substring(12, 16)
      )

      if (this.receiveData.temperatureThree.length > 3) {
        // 计算原数组平均值
        const oldAvg = this.calcTempThreeAvg()

        // 删除首位
        this.receiveData.temperatureThree.shift()
        // 添加新值
        this.receiveData.temperatureThree.push(temperatureThree)
        // 计算新的平均值
        const newAvg = this.calcTempThreeAvg()

        // 新老平均值不相等，则回调渲染进程
        if (['WavePage', 'WavePage.RealtimePower'].includes(this.currentPage)) {
          if (oldAvg !== newAvg) {
            this.invokeRender('wave-page-display', {
              temperatureThree: `${newAvg}`
            })
          }
        }
      } else {
        this.receiveData.temperatureThree.push(temperatureThree)

        if (['WavePage', 'WavePage.RealtimePower'].includes(this.currentPage)) {
          if (this.receiveData.temperatureThree.length > 3) {
            const avg = this.calcTempThreeAvg()

            this.invokeRender('wave-page-display', {
              temperatureThree: `${avg}`
            })
          }
        }
      }

      if (this.receiveData.temperatureFour.length > 3) {
        // 计算原数组平均值
        const oldAvg = this.calcTempFourAvg()
        // 删除首位
        this.receiveData.temperatureFour.shift()
        // 添加新值
        this.receiveData.temperatureFour.push(temperatureFour)
        // 计算新的平均值
        const newAvg = this.calcTempFourAvg()
        // 新老平均值不相等，则回调渲染进程
        if (['WavePage', 'WavePage.RealtimePower'].includes(this.currentPage)) {
          if (oldAvg !== newAvg) {
            this.invokeRender('wave-page-display', {
              temperatureFour: `${newAvg}`
            })
          }
        }
      } else {
        this.receiveData.temperatureFour.push(temperatureFour)

        if (['WavePage', 'WavePage.RealtimePower'].includes(this.currentPage)) {
          if (this.receiveData.temperatureFour.length > 3) {
            const avg = this.calcTempFourAvg()

            this.invokeRender('wave-page-display', {
              temperatureFour: `${avg}`
            })
          }
        }
      }
    }

    /***************** 全页面解析指令 - end *****************/

    // 主界面版本号
    if (this.currentPage === 'ActionChoose') {
      if (bufferStr.startsWith('07822100')) {
        const versionStr = bufferStr.substring(8, 16)
        // 判断是否初次收到前四位数据
        if (this.receiveData.mainboardPrefix.length) {
          // 不是初次收到数据，如果新的数据和前次数据前4位不一致，则替换，一致则不作任何处理
          if (this.receiveData.mainboardPrefix !== versionStr) {
            this.receiveData.mainboardPrefix = versionStr

            // 替换后，判断版本号是否接收全，如果已接收完全，则解析后回调渲染进程
            if (this.receiveData.mainboardSuffix.length) {
              const version = this.parser.parseMainboard(
                this.receiveData.mainboardPrefix +
                  this.receiveData.mainboardSuffix
              )
              this.invokeRender('action-choose-page', { version })
            }
          }
        } else {
          // 初次收到数据，直接赋值版本号前4位
          this.receiveData.mainboardPrefix = versionStr

          // 初次接收数据后，判断版本号是否接收全，如果已接收完全，则解析后回调渲染进程
          if (this.receiveData.mainboardSuffix.length) {
            const version = this.parser.parseMainboard(
              this.receiveData.mainboardPrefix +
                this.receiveData.mainboardSuffix
            )
            this.invokeRender('action-choose-page', { version })
          }
        }
      }

      // 此为版本号后两位数据
      if (bufferStr.startsWith('07822102')) {
        const versionStr = bufferStr.substring(8, 12)
        // 判断是否初次收到后两2位数据
        if (this.receiveData.mainboardSuffix.length) {
          // 不是初次收到数据，如果新的数据和前次数据后2位不一致，则替换，一致则不作任何处理
          if (this.receiveData.mainboardSuffix !== versionStr) {
            this.receiveData.mainboardSuffix = versionStr

            // 替换后，判断版本号是否接收全，如果已接收完全，则解析后回调渲染进程
            if (this.receiveData.mainboardPrefix.length > 8) {
              const version = this.parser.parseMainboard(
                this.receiveData.mainboardPrefix +
                  this.receiveData.mainboardSuffix
              )
              this.invokeRender('action-choose-page', { version })
            }
          }
        } else {
          // 初次收到数据，直接赋值版本号后2位
          this.receiveData.mainboardSuffix = versionStr

          // 初次接收数据后，判断版本号是否接收全，如果已接收完全，则解析后回调渲染进程
          if (this.receiveData.mainboardPrefix.length) {
            const version = this.parser.parseMainboard(
              this.receiveData.mainboardPrefix +
                this.receiveData.mainboardSuffix
            )
            this.invokeRender('action-choose-page', { version })
          }
        }
      }
    }

    // 功率校准界面解析
    if (this.currentPage === 'PowerCalibrationPageOneSend') {
      if (bufferStr.startsWith('15821030')) {
        const labelStr = bufferStr.substring(8)
        if (labelStr !== this.receiveData.powerLabel) {
          this.invokeRender('power-display', {
            label: this.parser.parsePowerLabel(bufferStr)
          })
          this.receiveData.powerLabel = labelStr
        }
      }

      if (bufferStr.startsWith('1b821020')) {
        const daStr = bufferStr.substring(8, 44)
        const stepStr = bufferStr.substring(44)
        if (daStr !== this.receiveData.powerDA) {
          this.invokeRender('power-display', {
            da: this.parser.parsePowerDA(bufferStr)
          })
          this.receiveData.powerDA = daStr
        }
        if (stepStr !== this.receiveData.powerStep) {
          this.invokeRender('power-display', {
            step: this.parser.parsePowerStep(stepStr)
          })
          this.receiveData.powerStep = stepStr
        }
      }
    }

    // T1温度校准
    if (this.currentPage === 'TemperatureCalibrationPageOneSend') {
      // 校准顺序错误
      if (bufferStr.startsWith('05823017')) {
        const orderStr = bufferStr.substring(8, 12)

        this.invokeRender('temperature-one-display', {
          orderFault: hexToNumber(orderStr)
        })
      }

      // 传感器错误
      if (bufferStr.startsWith('05823015')) {
        const sensorStr = bufferStr.substring(8, 12)

        this.invokeRender('temperature-one-display', {
          sensorFault: hexToNumber(sensorStr)
        })
      }

      // 传感器通讯错误
      if (bufferStr.startsWith('05823016')) {
        const communicationStr = bufferStr.substring(8, 12)

        this.invokeRender('temperature-one-display', {
          communicationFault: hexToNumber(communicationStr)
        })
      }

      // 温度
      if (bufferStr.startsWith('05823014')) {
        const temperature = this.parser.parseTemperature(
          bufferStr.substring(8, 12)
        )

        if (this.receiveData.temperatureOne.length > 3) {
          // 计算原数组平均值
          const oldAvg = this.calcTempOneAvg()
          // 删除首位
          this.receiveData.temperatureOne.shift()
          // 添加新值
          this.receiveData.temperatureOne.push(temperature)
          // 计算新的平均值
          const newAvg = this.calcTempOneAvg()
          // 新老平均值不相等，则回调渲染进程
          if (oldAvg !== newAvg) {
            this.invokeRender('temperature-one-display', {
              temperatureOne: `${newAvg}`
            })
          }
        } else {
          this.receiveData.temperatureOne.push(temperature)

          if (this.receiveData.temperatureOne.length > 3) {
            const avg = this.calcTempOneAvg()

            this.invokeRender('temperature-one-display', {
              temperatureOne: `${avg}`
            })
          }
        }
      }
    }

    // T2温度校准
    if (this.currentPage === 'TemperatureCalibrationPageTwoSend') {
      // 校准顺序错误
      if (bufferStr.startsWith('05823007')) {
        const orderStr = bufferStr.substring(8, 12)

        this.invokeRender('temperature-two-display', {
          orderFault: hexToNumber(orderStr)
        })
      }

      // 传感器错误
      if (bufferStr.startsWith('05823005')) {
        const sensorStr = bufferStr.substring(8, 12)

        this.invokeRender('temperature-two-display', {
          sensorFault: hexToNumber(sensorStr)
        })
      }

      // 传感器通讯错误
      if (bufferStr.startsWith('05823006')) {
        const communicationStr = bufferStr.substring(8, 12)

        this.invokeRender('temperature-two-display', {
          communicationFault: hexToNumber(communicationStr)
        })
      }

      // 温度
      if (bufferStr.startsWith('05823004')) {
        const temperature = this.parser.parseTemperature(
          bufferStr.substring(8, 12)
        )

        if (this.receiveData.temperatureTwo.length > 3) {
          // 计算原数组平均值
          const oldAvg = this.calcTempTwoAvg()
          // 删除首位
          this.receiveData.temperatureTwo.shift()
          // 添加新值
          this.receiveData.temperatureTwo.push(temperature)
          // 计算新的平均值
          const newAvg = this.calcTempTwoAvg()
          // 新老平均值不相等，则回调渲染进程
          if (oldAvg !== newAvg) {
            this.invokeRender('temperature-two-display', {
              temperatureTwo: `${newAvg}`
            })
          }
        } else {
          this.receiveData.temperatureTwo.push(temperature)

          if (this.receiveData.temperatureTwo.length > 3) {
            const avg = this.calcTempTwoAvg()

            this.invokeRender('temperature-two-display', {
              temperatureTwo: `${avg}`
            })
          }
        }
      }
    }

    // T3温度校准
    if (this.currentPage === 'TemperatureCalibrationPageThreeSend') {
      // 校准顺序错误
      if (bufferStr.startsWith('05823027')) {
        const orderStr = bufferStr.substring(8, 12)

        this.invokeRender('temperature-three-display', {
          orderFault: hexToNumber(orderStr)
        })
      }

      // 传感器错误
      if (bufferStr.startsWith('05823025')) {
        const sensorStr = bufferStr.substring(8, 12)

        this.invokeRender('temperature-three-display', {
          sensorFault: hexToNumber(sensorStr)
        })
      }

      // 传感器通讯错误
      if (bufferStr.startsWith('05823026')) {
        const communicationStr = bufferStr.substring(8, 12)

        this.invokeRender('temperature-three-display', {
          communicationFault: hexToNumber(communicationStr)
        })
      }

      // 温度
      if (bufferStr.startsWith('05823024')) {
        const temperature = this.parser.parseTemperature(
          bufferStr.substring(8, 12)
        )

        if (this.receiveData.temperatureThree.length > 3) {
          // 计算原数组平均值
          const oldAvg = this.calcTempThreeAvg()
          // 删除首位
          this.receiveData.temperatureThree.shift()
          // 添加新值
          this.receiveData.temperatureThree.push(temperature)
          // 计算新的平均值
          const newAvg = this.calcTempThreeAvg()
          // 新老平均值不相等，则回调渲染进程
          if (oldAvg !== newAvg) {
            this.invokeRender('temperature-three-display', {
              temperatureThree: `${newAvg}`
            })
          }
        } else {
          this.receiveData.temperatureThree.push(temperature)

          if (this.receiveData.temperatureThree.length > 3) {
            const avg = this.calcTempThreeAvg()

            this.invokeRender('temperature-three-display', {
              temperatureThree: `${avg}`
            })
          }
        }
      }
    }

    // T4温度校准
    if (this.currentPage === 'TemperatureCalibrationPageFourSend') {
      // 校准顺序错误
      if (bufferStr.startsWith('05823037')) {
        const orderStr = bufferStr.substring(8, 12)

        this.invokeRender('temperature-four-display', {
          orderFault: hexToNumber(orderStr)
        })
      }

      // 传感器错误
      if (bufferStr.startsWith('05823035')) {
        const sensorStr = bufferStr.substring(8, 12)

        this.invokeRender('temperature-four-display', {
          sensorFault: hexToNumber(sensorStr)
        })
      }

      // 传感器通讯错误
      if (bufferStr.startsWith('05823036')) {
        const communicationStr = bufferStr.substring(8, 12)

        this.invokeRender('temperature-four-display', {
          communicationFault: hexToNumber(communicationStr)
        })
      }

      // 温度
      if (bufferStr.startsWith('05823034')) {
        const temperature = this.parser.parseTemperature(
          bufferStr.substring(8, 12)
        )

        if (this.receiveData.temperatureFour.length > 3) {
          // 计算原数组平均值
          const oldAvg = this.calcTempFourAvg()
          // 删除首位
          this.receiveData.temperatureFour.shift()
          // 添加新值
          this.receiveData.temperatureFour.push(temperature)
          // 计算新的平均值
          const newAvg = this.calcTempFourAvg()
          // 新老平均值不相等，则回调渲染进程
          if (oldAvg !== newAvg) {
            this.invokeRender('temperature-four-display', {
              temperatureFour: `${newAvg}`
            })
          }
        } else {
          this.receiveData.temperatureFour.push(temperature)

          if (this.receiveData.temperatureFour.length > 3) {
            const avg = this.calcTempFourAvg()

            this.invokeRender('temperature-four-display', {
              temperatureFour: `${avg}`
            })
          }
        }
      }
    }

    // 波形界面
    if (['WavePage', 'WavePage.RealtimePower'].includes(this.currentPage)) {
      // 功率显示
      if (bufferStr.startsWith('05821001')) {
        const dataStr = bufferStr.substring(8, 12)

        if (dataStr !== this.receiveData.power) {
          this.invokeRender('wave-page-display', {
            power: hexToNumber(dataStr)
          })
          this.receiveData.power = dataStr
        }
      }

      // 周期占空比
      if (bufferStr.startsWith('07821002')) {
        const periodStr = bufferStr.substring(8, 12)
        const dutyRatioStr = bufferStr.substring(12, 16)
        // console.log(' period : ', bufferStr, periodStr)

        if (periodStr !== this.receiveData.period) {
          this.invokeRender('wave-page-display', {
            period: hexToNumber(periodStr)
          })
          this.receiveData.period = periodStr
        }

        if (dutyRatioStr !== this.receiveData.dutyRatio) {
          this.invokeRender('wave-page-display', {
            dutyRatio: hexToNumber(dutyRatioStr)
          })
          this.receiveData.dutyRatio = dutyRatioStr
        }
      }

      // T1、T2是否选中
      if (bufferStr.startsWith('07821013')) {
        const oneSelected = bufferStr.substring(8, 12)
        const twoSelected = bufferStr.substring(12, 16)

        if (oneSelected !== this.receiveData.temperatureOneSelected) {
          this.invokeRender('wave-page-display', {
            temperatureOneSelected: hexToNumber(oneSelected)
          })
          this.receiveData.temperatureOneSelected = oneSelected
        }

        if (twoSelected !== this.receiveData.temperatureTwoSelected) {
          this.invokeRender('wave-page-display', {
            temperatureTwoSelected: hexToNumber(twoSelected)
          })
          this.receiveData.temperatureTwoSelected = twoSelected
        }
      }

      // 音量、处方号
      if (bufferStr.startsWith('07821015')) {
        const volumeStr = bufferStr.substring(8, 12)
        const prescriptionIdStr = bufferStr.substring(12, 16)
        // console.log(' volume : ', bufferStr, volumeStr)

        if (this.receiveData.volume === '') {
          this.invokeRender('wave-page-display', {
            volume: hexToNumber(volumeStr)
          })
          this.receiveData.volume = volumeStr
        }

        if (this.receiveData.prescriptionId === '') {
          this.invokeRender('wave-page-display', {
            prescriptionId: hexToNumber(prescriptionIdStr)
          })
          this.receiveData.prescriptionId = prescriptionIdStr
        }
      }

      // 静音锁屏图标
      if (bufferStr.startsWith('07821017')) {
        const muteStr = bufferStr.substring(8, 12)
        const lockscreenStr = bufferStr.substring(12, 16)

        if (muteStr !== this.receiveData.mute) {
          this.invokeRender('wave-page-display', {
            mute: hexToNumber(muteStr)
          })
          this.receiveData.mute = muteStr
        }

        if (lockscreenStr !== this.receiveData.lockscreen) {
          this.invokeRender('wave-page-display', {
            lockscreen: hexToNumber(lockscreenStr)
          })
          this.receiveData.lockscreen = lockscreenStr
        }
      }

      // 处方号显示
      if (bufferStr.startsWith('05821016')) {
        const prescriptionIdStr = bufferStr.substring(8, 12)

        if (prescriptionIdStr !== this.receiveData.prescriptionId) {
          this.invokeRender('wave-page-display', {
            prescriptionId: hexToNumber(prescriptionIdStr)
          })
          this.receiveData.prescriptionId = prescriptionIdStr
        }
      }

      // 辐射器显示
      if (bufferStr.startsWith('0582101a')) {
        const radiatorStr = bufferStr.substring(8, 12)

        if (radiatorStr !== this.receiveData.radiator) {
          this.invokeRender('wave-page-display', {
            radiator: hexToNumber(radiatorStr)
          })
          this.receiveData.radiator = radiatorStr
        }
      }

      // 辐射器是否选中
      if (bufferStr.startsWith('0582101c')) {
        const radiatorSelectedStr = bufferStr.substring(8, 12)
        if (radiatorSelectedStr !== this.receiveData.radiatorSelected) {
          this.invokeRender('wave-page-display', {
            // 0 - 选中，1 - 未选中，所以此处取反
            radiatorSelected: !hexToNumber(radiatorSelectedStr)
          })
          this.receiveData.radiatorSelected = radiatorSelectedStr
        }
      }

      // 音量显示
      if (bufferStr.startsWith('05821015')) {
        const volumeStr = bufferStr.substring(8, 12)
        // console.log(' volume : ', bufferStr, volumeStr)

        if (volumeStr !== this.receiveData.volume) {
          this.invokeRender('wave-page-display', {
            volume: hexToNumber(volumeStr)
          })
          this.receiveData.volume = volumeStr
        }
      }

      // 静音显示
      if (bufferStr.startsWith('05821017')) {
        const muteStr = bufferStr.substring(8, 12)

        if (muteStr !== this.receiveData.mute) {
          this.invokeRender('wave-page-display', {
            mute: hexToNumber(muteStr)
          })
          this.receiveData.mute = muteStr
        }
      }

      // 锁屏显示
      if (bufferStr.startsWith('07821018')) {
        const lockscreenStr = bufferStr.substring(8, 12)

        if (lockscreenStr !== this.receiveData.lockscreen) {
          this.invokeRender('wave-page-display', {
            lockscreen: hexToNumber(lockscreenStr)
          })
          this.receiveData.lockscreen = lockscreenStr
        }
      }

      // 测温传感器故障
      if (bufferStr.startsWith('0582100a')) {
        const sensorFaultStr = bufferStr.substring(8, 12)
        // console.log('this.receiveData.sensorFault : ', sensorFaultStr)

        if (sensorFaultStr !== this.receiveData.sensorFault) {
          this.invokeRender('wave-page-display', {
            sensorFault: hexToNumber(sensorFaultStr)
          })
          this.receiveData.sensorFault = sensorFaultStr
        }
      }

      // 测温通讯故障
      if (bufferStr.startsWith('0582100b')) {
        const communicationFaultStr = bufferStr.substring(8, 12)

        if (communicationFaultStr !== this.receiveData.communicationFault) {
          this.invokeRender('wave-page-display', {
            communicationFault: hexToNumber(communicationFaultStr)
          })
          this.receiveData.communicationFault = communicationFaultStr
        }
      }

      // 倒计时，指示灯
      // if (bufferStr.startsWith('0d82100520')) {
      if (bufferStr.startsWith('0d821005')) {
        const countdownStr = bufferStr.substring(8, 20)
        // console.log(
        //   'countdownStr : ',
        //   bufferStr,
        //   countdownStr,
        //   this.parser.parseCountdown(countdownStr)
        // )
        const lightStr = bufferStr.substring(24)

        if (countdownStr !== this.receiveData.countdown) {
          this.invokeRender('wave-page-display', {
            countdown: this.parser.parseCountdown(countdownStr)
          })
          this.receiveData.countdown = countdownStr
        }

        if (lightStr !== this.receiveData.light) {
          this.invokeRender('wave-page-display', {
            light: hexToNumber(lightStr)
          })
          this.receiveData.light = lightStr
        }
      }

      // 新增T3，T4是否选中逻辑
      // T3、T4是否选中
      if (bufferStr.startsWith('07824003')) {
        const threeSelected = bufferStr.substring(8, 12)
        const fourSelected = bufferStr.substring(12, 16)

        if (threeSelected !== this.receiveData.temperatureThreeSelected) {
          this.invokeRender('wave-page-display', {
            temperatureThreeSelected: hexToNumber(threeSelected)
          })
          this.receiveData.temperatureThreeSelected = threeSelected
        }

        if (fourSelected !== this.receiveData.temperatureFourSelected) {
          this.invokeRender('wave-page-display', {
            temperatureFourSelected: hexToNumber(fourSelected)
          })
          this.receiveData.temperatureFourSelected = fourSelected
        }
      }

      // 设定温度显示
      if (bufferStr.startsWith('05824000')) {
        const dataStr = bufferStr.substring(8)

        if (dataStr !== this.receiveData.settingTemperature) {
          this.invokeRender('wave-page-display', {
            settingTemperature: `${hexToNumber(dataStr) * 10}`
          })
          this.receiveData.settingTemperature = dataStr
        }
      }
    }

    // 解析实时功率
    // 实时功率
    if (bufferStr.startsWith('0582101b')) {
      const realtimePowerStr = bufferStr.substring(8, 12)
      // console.log('realtime power str : ', bufferStr, realtimePowerStr)

      if (realtimePowerStr !== this.receiveData.realtimePower) {
        // console.log(
        //   'invoke render realtime power : ',
        //   realtimePowerStr,
        //   this.receiveData.realtimePower
        // )
        this.invokeRender('wave-page-display', {
          realtimePower: hexToNumber(realtimePowerStr)
        })
        this.receiveData.realtimePower = realtimePowerStr
      }
    }
  }
}
