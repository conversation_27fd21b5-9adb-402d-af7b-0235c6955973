<script setup lang="ts">
import PageWrapper from '@/components/PageWrapper/index.vue'
import { reactive, ref, onMounted, onUnmounted } from 'vue';
import { useRouter } from 'vue-router';
import { useCalibrationStore } from '@/store/modules/calibration'

defineOptions({
  name: 'PowerCalibrationPage'
})

const calibrationStore = useCalibrationStore()
const router = useRouter()
const selected = ref<number>(1)

function changeSelected(num: number) {
  selected.value = num

  let method: string = 'PowerCalibrationPageOneSend.One'
  switch (num) {
    case 1:
      method = 'PowerCalibrationPageOneSend.One'
      break;
    case 2:
      method = 'PowerCalibrationPageOneSend.Five'
      break;
    case 3:
      method = 'PowerCalibrationPageOneSend.Ten'
      break;
    case 4:
      method = 'PowerCalibrationPageOneSend.Twenty'
      break;
    case 5:
      method = 'PowerCalibrationPageOneSend.Forty'
      break;
    case 6:
      method = 'PowerCalibrationPageOneSend.Sixty'
      break;
    case 7:
      method = 'PowerCalibrationPageOneSend.Hundred'
      break;
    case 8:
      method = 'PowerCalibrationPageOneSend.OneHundredAndFifty'
      break;
    case 9:
      method = 'PowerCalibrationPageOneSend.TwoHundred'
      break;
    default:
      break;
  }

  window.electronAPI.serialport.dispatch(method)
}

const longStep = ref<boolean>(false)

function changeStep() {
  longStep.value = !longStep.value
  window.electronAPI.serialport.dispatch('PowerCalibrationPageOneSend.ToggleStepValue')
}

function handlePowerChange(type: 'up' | 'down') {
  let method = 'PowerCalibrationPageOneSend.Up'

  if (type === 'down') {
    method = 'PowerCalibrationPageOneSend.Down'
  }

  window.electronAPI.serialport.dispatch(method)
}

function handlePause() {
  window.electronAPI.serialport.dispatch('PowerCalibrationPageOneSend.Pause')
}

const confirming = ref(false)
function handleConfirm() {
  confirming.value = true
  window.electronAPI.serialport.dispatch('PowerCalibrationPageOneSend.Confirm')
}

function handleLogout() {
  window.electronAPI.serialport.dispatch('PowerCalibrationPageOneSend.Exit')
  calibrationStore.setFirstIn(true)
  setTimeout(() => {
    router.replace('/')
  }, 3500)
}

const state = reactive<{ label: number[], da: (string | number)[], step: string }>({
  label: [1, 5, 10, 20, 40, 60, 100, 150, 200],
  da: ['', '', '', '', '', '', '', '', ''],
  step: ''
})

onMounted(() => {
  // 开启功率校准界面循环解析
  window.electronAPI.serialport.dispatch('CurrentPage.PowerCalibrationPageOneSend')

  window.electronAPI.serialport.receive('power-display', (_, { label, da, step }: { label?: number[], da?: number[], step?: string }) => {
    if (label) {
      state.label = label
    }

    if (da) {
      state.da = da
    }

    if (step !== undefined) {
      state.step = step
    }
  })
})

onUnmounted(() => {
  window.electronAPI.serialport.removeListener('power-display')
})
</script>

<template>
  <PageWrapper show-logo :logoutAutoBack="false" @logout="handleLogout">
    <div class="content-wrapper flex-center h-[800px]">
      <div class="h-full pt-36">
        <div class="flex mb-10">
          <div class="w-40 mr-16">
            <p class="mb-4 leading-6 min-h-6 text-center">{{ state.da[0] }}</p>
            <div class="w-full h-12 rounded-md shadow-xl text-center relative flex justify-center items-center"
              :class="{ 'bg-power-selected': selected === 1, 'bg-power-label': selected !== 1 }"
              @click="changeSelected(1)">
              <span class="text-xl">{{ state.label[0] }}</span>
              <span class="absolute top-0 right-0 text-center w-10 h-full text-lg leading-10 pt-1">W</span>
            </div>
          </div>
          <div class="w-40 mr-16">
            <p class="mb-4 leading-6 min-h-6 text-center">{{ state.da[1] }}</p>
            <div class="w-full h-12 rounded-md shadow-xl text-center relative flex justify-center items-center"
              :class="{ 'bg-power-selected': selected === 2, 'bg-power-label': selected !== 2 }"
              @click="changeSelected(2)">
              <span class="text-xl">{{ state.label[1] }}</span>
              <span class="absolute top-0 right-0 text-center w-10 h-full text-lg leading-10 pt-1">W</span>
            </div>
          </div>
          <div class="w-40 mr-16">
            <p class="mb-4 leading-6 min-h-6 text-center">{{ state.da[2] }}</p>
            <div class="w-full h-12 rounded-md shadow-xl text-center relative flex justify-center items-center"
              :class="{ 'bg-power-selected': selected === 3, 'bg-power-label': selected !== 3 }"
              @click="changeSelected(3)">
              <span class="text-xl">{{ state.label[2] }}</span>
              <span class="absolute top-0 right-0 text-center w-10 h-full text-lg leading-10 pt-1">W</span>
            </div>
          </div>
        </div>
        <div class="flex mb-10">
          <div class="w-40 mr-16">
            <p class="mb-4 leading-6 min-h-6 text-center">{{ state.da[3] }}</p>
            <div class="w-full h-12 rounded-md shadow-xl text-center relative flex justify-center items-center"
              :class="{ 'bg-power-selected': selected === 4, 'bg-power-label': selected !== 4 }"
              @click="changeSelected(4)">
              <span class="text-xl">{{ state.label[3] }}</span>
              <span class="absolute top-0 right-0 text-center w-10 h-full text-lg leading-10 pt-1">W</span>
            </div>
          </div>
          <div class="w-40 mr-16">
            <p class="mb-4 leading-6 min-h-6 text-center">{{ state.da[4] }}</p>
            <div class="w-full h-12 rounded-md shadow-xl text-center relative flex justify-center items-center"
              :class="{ 'bg-power-selected': selected === 5, 'bg-power-label': selected !== 5 }"
              @click="changeSelected(5)">
              <span class="text-xl">{{ state.label[4] }}</span>
              <span class="absolute top-0 right-0 text-center w-10 h-full text-lg leading-10 pt-1">W</span>
            </div>
          </div>
          <div class="w-40 mr-16">
            <p class="mb-4 leading-6 min-h-6 text-center">{{ state.da[5] }}</p>
            <div class="w-full h-12 rounded-md shadow-xl text-center relative flex justify-center items-center"
              :class="{ 'bg-power-selected': selected === 6, 'bg-power-label': selected !== 6 }"
              @click="changeSelected(6)">
              <span class="text-xl">{{ state.label[5] }}</span>
              <span class="absolute top-0 right-0 text-center w-10 h-full text-lg leading-10 pt-1">W</span>
            </div>
          </div>
        </div>
        <div class="flex mb-10">
          <div class="w-40 mr-16">
            <p class="mb-4 leading-6 min-h-6 text-center">{{ state.da[6] }}</p>
            <div class="w-full h-12 rounded-md shadow-xl text-center relative flex justify-center items-center"
              :class="{ 'bg-power-selected': selected === 7, 'bg-power-label': selected !== 7 }"
              @click="changeSelected(7)">
              <span class="text-xl">{{ state.label[6] }}</span>
              <span class="absolute top-0 right-0 text-center w-10 h-full text-lg leading-10 pt-1">W</span>
            </div>
          </div>
          <div class="w-40 mr-16">
            <p class="mb-4 leading-6 min-h-6 text-center">{{ state.da[7] }}</p>
            <div class="w-full h-12 rounded-md shadow-xl text-center relative flex justify-center items-center"
              :class="{ 'bg-power-selected': selected === 8, 'bg-power-label': selected !== 8 }"
              @click="changeSelected(8)">
              <span class="text-xl">{{ state.label[7] }}</span>
              <span class="absolute top-0 right-0 text-center w-10 h-full text-lg leading-10 pt-1">W</span>
            </div>
          </div>
          <div class="w-40 mr-16">
            <p class="mb-4 leading-6 min-h-6 text-center">{{ state.da[8] }}</p>
            <div class="w-full h-12 rounded-md shadow-xl text-center relative flex justify-center items-center"
              :class="{ 'bg-power-selected': selected === 9, 'bg-power-label': selected !== 9 }"
              @click="changeSelected(9)">
              <span class="text-xl">{{ state.label[8] }}</span>
              <span class="absolute top-0 right-0 text-center w-10 h-full text-lg leading-10 pt-1">W</span>
            </div>
          </div>
        </div>
        <div class="flex justify-center mt-20">
          <!-- <div v-if="!!state.exit"
            class="w-36 h-10 rounded-md shadow-xl text-center bg-accent mr-14 leading-10 cursor-pointer">
            请退出</div> -->
          <div class="w-36 h-10 rounded-md shadow-xl text-center bg-accent mr-14 leading-10 cursor-pointer"
            @click="changeStep">{{ state.step }}</div>
          <div class="w-36 h-10 flex justify-between">
            <span class="w-10 h-full text-center leading-10 bg-nova cursor-pointer rounded-lg p-2 select-none"
              @click="handlePowerChange('up')">
              <img class="w-full h-full" src="@/assets/img/top.png" alt=""></span>
            <span class="w-10 h-full text-center leading-10 bg-nova cursor-pointer rounded-lg p-2 select-none"
              @click="handlePowerChange('down')"><img class="w-full h-full" src="@/assets/img/bot.png" alt=""></span>
          </div>
        </div>
      </div>
    </div>
    <div class="exit_new1 bg-exit" @click="handleConfirm">确认</div>
    <div class="exit_new2 bg-exit" @click="handlePause">暂停</div>
  </PageWrapper>
</template>
<style scoped>
.exit_new1,
.exit_new2 {
  position: absolute;
  bottom: 55px;
  right: 80px;
  width: 120px;
  height: 48px;
  line-height: 48px;
  color: #000;
  border-radius: 4px;
  text-align: center;
  font-weight: 700;
  font-size: 18px;
  cursor: pointer;
  right: 220px;
  bottom: 55px;
}

.exit_new2 {
  right: 360px;
}
</style>
<script lang="ts">
export default {
  name: 'PowerCalibration'
}
</script>
