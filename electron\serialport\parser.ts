import { hexToNumber } from './util'
import iconv from 'iconv-lite'

export default class InstructionParser {
  private instructionMap: Record<string, string>

  constructor() {
    this.instructionMap = {
      '821001': 'parsePower',
      '821011': 'parseWaveTemperature',
      '82100a': 'parseWaveTemperature',
      '82100b': 'parseWaveCommunicationFault',
      '82101b': 'parseRealtimePower',
      '82101a': 'parseRadiator'
    }
  }

  public parse(buffer: Buffer) {
    const hex = buffer.toString()
    const keyAddress = hex.substring(2, 8)

    if (this.instructionMap[keyAddress]) {
      const data = this[this.instructionMap[keyAddress]]

      if (data) return data
    }

    return null
  }

  // 解析主控板版本
  public parseMainboard(hex: string) {
    const buf = Buffer.from(hex, 'hex')

    return iconv.decode(buf, 'gbk')
  }

  // 解析功率显示
  public parsePower(hex: string) {
    const powerHex = hex.substring(8, 12)
    return { power: hexToNumber(powerHex) }
  }

  // 波形界面解析温度
  public parseWaveTemperature(hex: string) {
    const oneHex = hex.substring(8, 12)
    const twoHex = hex.substring(12, 16)

    return {
      temperatureOne: hexToNumber(oneHex),
      temperatureTwo: hexToNumber(twoHex)
    }
  }

  // 波形界测温传感器故障
  public parseWaveSensorFault(hex: string) {
    const faultHex = hex.substring(8, 12)

    return {
      sensorFault: hexToNumber(faultHex)
    }
  }

  // 波形界测温通讯故障
  public parseWaveCommunicationFault(hex: string) {
    const faultHex = hex.substring(8, 12)

    return {
      communicationFault: hexToNumber(faultHex)
    }
  }

  // 波形界面实时功率
  public parseRealtimePower(hex: string) {
    const powerHex = hex.substring(8, 12)

    return {
      realtimePower: hexToNumber(powerHex)
    }
  }

  // 解析辐射器
  public parseRadiator(hex: string) {
    const radiatorHex = hex.substring(8, 12)

    return {
      radiator: hexToNumber(radiatorHex)
    }
  }

  // 解析温度T1、T2、T3、T4
  public parseTemperature(hex: string) {
    const temp = hexToNumber(hex)
    if (temp < 10000) return hexToNumber(hex)
    else return temp % 10000
  }

  // 功率校准界面标签
  public parsePowerLabel(hex: string) {
    const startIndex = [8, 12, 16, 20, 24, 28, 32, 36, 40]
    const data: number[] = []
    startIndex.forEach(start =>
      data.push(parseInt(hex.substring(start, start + 4), 16))
    )

    return data
  }

  // 功率校准界面解析DA值
  public parsePowerDA(hex: string) {
    const data: number[] = []
    const startIndex = [8, 12, 16, 20, 24, 28, 32, 36, 40, 44, 48, 52]
    startIndex.forEach(start =>
      data.push(parseInt(hex.substring(start, start + 4), 16))
    )

    return data
  }

  // 功率校准界面解析 ±1/10
  public parsePowerStep(hex: string) {
    let hexStr = hex
    if (hex.endsWith('0000')) hexStr = hex.replace(/0000$/, '')
    const buf = Buffer.from(hexStr, 'hex')

    return iconv.decode(buf, 'gbk')
  }

  // 解析波形界面倒計時
  public parseCountdown(hex: string) {
    const buf = Buffer.from(hex, 'hex')

    return iconv.decode(buf, 'gbk')
  }
}
