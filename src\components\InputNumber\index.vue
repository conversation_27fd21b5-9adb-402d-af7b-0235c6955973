<script lang="ts" setup>
import { onUnmounted } from 'vue';
import { Minus, Plus } from '@element-plus/icons-vue'
import { vRepeatClick } from '@/directives/repeat-click'
import 'element-plus/es/components/input-number/style/css'

const props = defineProps({
  modelValue: {
    type: [Number, String],
    default: 0
  },
  min: {
    type: Number,
    required: false,
    default: 1
  },
  max: {
    type: Number,
    required: false,
    default: Infinity
  },
  canAction: {
    type: Boolean,
    required: true,
    default: true
  }
})
const emits = defineEmits(['add', 'minus'])

function increase() {
  if (props.canAction) {
    emits('add')
  }
}

function decrease() {
  if (props.canAction) {
    emits('minus')
  }
}
</script>

<template>
  <div class="el-input-number" @dragstart.prevent>
    <span v-repeat-click="decrease" role="button" aria-label="decrease number" class="el-input-number__decrease"
      @keydown.enter="decrease">
      <el-icon>
        <minus />
      </el-icon>
    </span>
    <span v-repeat-click="increase" role="button" aria-label="increase number" class="el-input-number__increase"
      @keydown.enter="increase">
      <el-icon>
        <plus />
      </el-icon>
    </span>
    <el-input ref="input" :model-value="props.modelValue" readonly :max="max" :min="min"
      :validate-event="false" @wheel.prevent @keydown.up.prevent="increase" @keydown.down.prevent="decrease" />
  </div>
</template>

<script lang="ts">
export default {
  name: 'InputNumber'
}
</script>

<style scoped>
.el-input-number__decrease {
  background-color: var(--el-color-primary);
  color: #fff;
}

.el-input-number__increase {
  background-color: var(--el-color-primary);
  color: #fff;
}

.el-input-number__decrease:hover,
.el-input-number__increase:hover {
  color: #fff;
}

.el-input-number__decrease:active,
.el-input-number__increase:active {
  background-color: var(--el-color-primary-dark-2);
}
</style>
