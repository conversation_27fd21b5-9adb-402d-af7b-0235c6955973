<script setup lang="ts">
import { ref, withDefaults, watch, computed } from 'vue';
import { onClickOutside } from '@vueuse/core'

type ISelectOption = {
  id: number
  name: string
  duration?: number
  method?: string
  [key: string]: unknown
}

type ISelectProps = {
  options?: ISelectOption[]
  value: number | null
  boot?: boolean
  lockscreen?: boolean
}

const props = withDefaults(defineProps<ISelectProps>(), {
  options: () => [
    { id: 1, name: '连续波', duration: 15, method: 'PrescriptionSend.One', url: 'continuation' },
    { id: 2, name: '连续波', duration: 20, method: 'PrescriptionSend.Two', url: 'continuation' },
    { id: 3, name: '连续波', duration: 30, method: 'PrescriptionSend.Three', url: 'continuation' },
    { id: 4, name: '脉冲波', duration: 15, method: 'PrescriptionSend.Four', url: 'pulse' },
    { id: 5, name: '脉冲波', duration: 20, method: 'PrescriptionSend.Five', url: 'pulse' },
    { id: 6, name: '脉冲波', duration: 30, method: 'PrescriptionSend.Six', url: 'pulse' },
    { id: 7, name: '正弦波', duration: 15, method: 'PrescriptionSend.Seven', url: 'sine' },
    { id: 8, name: '正弦波', duration: 20, method: 'PrescriptionSend.Eight', url: 'sine' },
    { id: 9, name: '正弦波', duration: 30, method: 'PrescriptionSend.Nine', url: 'sine' },
    { id: 10, name: '三角波', duration: 15, method: 'PrescriptionSend.Ten', url: 'triangle' },
    { id: 11, name: '三角波', duration: 20, method: 'PrescriptionSend.Eleven', url: 'triangle' },
    { id: 12, name: '三角波', duration: 30, method: 'PrescriptionSend.Twelve', url: 'triangle' },
  ],
  value: null,
  boot: false,
  lockscreen: false
})

// 7016.93

const emits = defineEmits(['update:value', 'option-click', 'lock'])

const innerValue = ref<string>()

watch(() => props.value, val => {
  if (val && props.options.length) {
    const target = props.options.find(item => item.id === val)
    innerValue.value = target?.name
  } else {
    innerValue.value = ''
  }
})

const innerOptions = ref<ISelectOption[]>(props.options)

function filterOptions(keyword: string | undefined) {
  if (keyword) {
    return props.options?.filter(o => `处方${o.id}：${o.name} -（${o?.duration}分钟）`.includes(keyword))
  }

  return props.options
}

watch(innerValue, val => {
  if (mode.value === 'search') innerOptions.value = filterOptions(val)
})

const showOptions = ref(false)
const optionsRef = ref(null)
type SelectMode = 'search' | 'click'
const mode = ref<SelectMode>('click')

onClickOutside(optionsRef, () => showOptions.value = false)

function handleInputClick() {
  if (props.boot || props.lockscreen) {
    emits('lock')
  } else {
    showOptions.value = true
    innerOptions.value = props.options
  }
}

function handleOptionClick(option: ISelectOption) {
  if (mode.value === 'search') mode.value = 'click'
  emits('update:value', option.id)
  showOptions.value = false
  window.electronAPI.serialport.dispatch(option.method!)
  emits('option-click', option)
}

function handleInputChange(event: Event) {
  if (mode.value === 'click') mode.value = 'search'
  const input = event.target as HTMLInputElement

  if (mode.value === 'search') innerOptions.value = filterOptions(input.value)
}

const prescriptionName = computed(() => {
  const target = props.options.find(item => item.id === props.value)

  return target ? `${target.id}` : props.value
})
</script>



<template>
  <div class="relative">
    <input v-model="prescriptionName" type="text"
      class="input input-bordered input-primary w-full text-gray-900 text-center text-xl" readonly
      @input="handleInputChange" @click="handleInputClick">
    <div v-if="showOptions" ref="optionsRef"
      class="absolute w-full max-h-96 top-14 rounded-md border border-primary shadow-md shadow-primary-focus overflow-y-scroll scrollbar-hidden bg-white text-gray-900 z-10 py-2">
      <template v-if="innerOptions?.length">
        <div v-for="option in innerOptions" :key="'option-item-' + option.id"
          class="py-1 pl-4 pr-4 text-lg cursor-pointer" @click="handleOptionClick(option)">
          {{ `处方${option.id}：${option.name}（${option?.duration}分钟）` }}</div>
      </template>
      <template v-else>
        <div class="text-center py-2 px-4">暂无数据</div>
      </template>
    </div>
  </div>
</template>

<script lang="ts">
export default {
  name: 'Select'
}
</script>
