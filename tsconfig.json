{
  "compilerOptions": {
    "target": "ESNext",
    "useDefineForClassFields": true,
    "module": "ESNext",
    "moduleResolution": "Node",
    "strict": true,
    "jsx": "preserve",
    "baseUrl": ".",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "esModuleInterop": true,
    "experimentalDecorators": true,
    "lib": [
      "ESNext",
      "DOM"
    ],
    "skipLibCheck": true,
    "noEmit": true,
    "paths": {
      "@/*": [
        "src/*"
      ],
      "@db/*": [
        "db/*"
      ]
    },
  },
  "include": [
    "src",
    "auto-imports.d.ts"
  ],
  "references": [
    {
      "path": "./tsconfig.node.json"
    }
  ],
  "types": [
    "element-plus/global"
  ]
}
