import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  OneToMany
} from 'typeorm'
import PatientCase from './patient-case.entity'

@Entity({ name: 'patient' }) // 实体
export default class Patient {
  @PrimaryGeneratedColumn() // 自增主键
  id: number

  @Column({ type: 'varchar' }) // 姓名
  name: string // js 数据类型

  @Column({ type: 'varchar' }) // 病历编号(可以是社保卡号)
  sn: string // js 数据类型

  @Column({ name: 'age', type: 'int' }) // 年龄
  age: number

  @Column({ name: 'sex', type: 'int' }) // 性别：0 - 男，1 - 女
  sex: number

  @Column({ name: 'weight', type: 'varchar', nullable: true }) // 体重，非必填
  weight: string

  @Column({ name: 'doctor_name', type: 'varchar', nullable: true }) // 治疗医生，非必填
  doctorName: string

  @OneToMany(() => PatientCase, patientCase => patientCase.patient)
  cases: PatientCase[]

  @Column({ name: 'creator_id', type: 'int' })
  creatorId: number

  @CreateDateColumn({
    type: 'datetime',
    name: 'created_time',
    comment: '创建时间'
  })
  createdTime: Date

  @UpdateDateColumn({
    type: 'datetime',
    name: 'updated_time',
    comment: '更新时间'
  })
  updateTime: Date

  @DeleteDateColumn({
    type: 'datetime',
    name: 'deleted_time',
    nullable: true,
    comment: '删除时间'
  })
  deletedTime: Date
}
