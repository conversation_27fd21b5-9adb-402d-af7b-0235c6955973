import { rmSync } from 'node:fs'
import { resolve } from 'node:path'
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import electron from 'vite-plugin-electron'
import pkg from './package.json'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import copy from 'rollup-plugin-copy'
import { viteCommonjs } from '@originjs/vite-plugin-commonjs'
import { viteStaticCopy } from 'vite-plugin-static-copy'

function pathResolve(dir: string) {
  return resolve(process.cwd(), '.', dir)
}

// https://vitejs.dev/config/
export default defineConfig(({ command }) => {
  rmSync('dist-electron', { recursive: true, force: true })

  const isServe = command === 'serve'
  const isBuild = command === 'build'
  const sourcemap = isServe || !!process.env.VSCODE_DEBUG

  return {
    resolve: {
      alias: [
        // @/xxxx => src/xxxx
        {
          find: /@\//,
          replacement: pathResolve('src') + '/'
        },
        {
          find: /@db\//,
          replacement: pathResolve('db') + '/'
        }
      ]
    },
    plugins: [
      vue(),
      AutoImport({
        resolvers: [ElementPlusResolver()]
      }),
      Components({
        resolvers: [ElementPlusResolver()]
      }),
      // for dicom-parser
      viteCommonjs({
        include: ['dicom-parser']
      }),
      viteStaticCopy({
        targets: [
          {
            // 从 node_modules 复制 DICOM worker 文件
            src: 'node_modules/@cornerstonejs/dicom-image-loader/dist/esm/decodeImageFrameWorker.js',
            // 复制到 public 目录，这样可以通过相对路径访问
            dest: 'workers'
          },
          {
            // 复制相关的依赖文件
            src: 'node_modules/@cornerstonejs/dicom-image-loader/dist/esm/*.wasm',
            dest: 'workers'
          }
        ]
      }),
      electron([
        {
          // Main-Process entry file of the Electron App.
          entry: 'electron/main/index.ts',
          onstart(options) {
            if (process.env.VSCODE_DEBUG) {
              console.log(
                /* For `.vscode/.debug.script.mjs` */ '[startup] Electron App'
              )
            } else {
              options.startup()
            }
          },
          vite: {
            build: {
              sourcemap,
              minify: isBuild,
              outDir: 'dist-electron/main',
              rollupOptions: {
                external: [
                  ...Object.keys('dependencies' in pkg ? pkg.dependencies : {}),
                  'electron/serialport/sqlite.worker.ts'
                ]
              }
            }
          }
        },
        {
          entry: 'electron/preload/index.ts',
          onstart(options) {
            // Notify the Renderer-Process to reload the page when the Preload-Scripts build is complete,
            // instead of restarting the entire Electron App.
            options.reload()
          },
          vite: {
            build: {
              sourcemap,
              minify: isBuild,
              outDir: 'dist-electron/preload',
              rollupOptions: {
                external: Object.keys(
                  'dependencies' in pkg ? pkg.dependencies : {}
                )
              }
            }
          }
        },
        {
          entry: 'electron/db/sqlite.worker.ts',
          vite: {
            build: {
              minify: isBuild,
              outDir: 'dist-electron/main',
              rollupOptions: {
                external: [
                  ...Object.keys('dependencies' in pkg ? pkg.dependencies : {})
                ]
              }
            }
          }
        }
      ])
      // Use Node.js API in the Renderer-process
      // renderer({
      //   nodeIntegration: true
      // })
      // commonjs()
    ],
    server: process.env.VSCODE_DEBUG
      ? (() => {
          const url = new URL(pkg.debug.env.VITE_DEV_SERVER_URL)
          return {
            host: url.hostname,
            port: +url.port
          }
        })()
      : {
          port: 35173
        },
    clearScreen: false,
    build: {
      rollupOptions: {
        plugins: [
          copy({
            targets: [
              {
                src: [
                  'public/SourceHanSansCN-Bold.otf',
                  'public/SourceHanSansCN-Normal.otf'
                ],
                dest: 'dist/assets/'
              },
              {
                src: 'public/SourceHanSansCN-Bold.otf',
                dest: 'dist/assets/'
              }
            ]
          })
        ]
      }
    }
    // seems like only required in dev mode
    // optimizeDeps: {
    //   exclude: ['@cornerstonejs/dicom-image-loader'],
    //   include: ['dicom-parser']
    // }
  }
})
