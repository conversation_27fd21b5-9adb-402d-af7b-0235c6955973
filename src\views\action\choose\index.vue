<script setup lang="ts">
import PageWrapper from '@/components/PageWrapper/index.vue'
import { ref } from 'vue';
import { useRouter } from 'vue-router'
import { useMedicalStore } from '@/store/modules/medical';

defineOptions({
  name: 'ActionChoosePage'
})

const router = useRouter()
const medicalStore = useMedicalStore()

function goPage(action: string) {
  router.push({ path: `/action/${action}` })
}

function handleMicrowave() {
  window.electronAPI.serialport.dispatch('ActionChoose.MicrowaveCure')

  setTimeout(() => {
    medicalStore.resetFaultState()

    goPage('cure/continuation')
  }, 300)
}

function handleCalibration() {
  window.electronAPI.serialport.dispatch('ActionChoose.CorrectTemperature')
  setTimeout(() => {
    goPage('calibration')
  }, 300)
}

function handleLogout() {
  router.replace('/home')
}
</script>

<template>
  <PageWrapper :logoutAutoBack="false" show-logo @logout="handleLogout">
    <div class="content-wrapper flex-center h-[800px]">
      <div class="flex-center">
        <button key="microwaveButton" class="menu-item mr-12" @click="handleMicrowave">微波治疗</button>
        <button key="calibrationButton" class="menu-item" @click="handleCalibration">校准温度</button>
      </div>
    </div>
  </PageWrapper>
</template>
<style>
.codkd {
  border-radius: 5px;
  padding: 5px 15px;
  color: #000;
  background: #fff;
  border: solid 1px #bbb;
}
</style>
