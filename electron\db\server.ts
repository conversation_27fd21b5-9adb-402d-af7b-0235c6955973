import {
  DataSource,
  Like,
  Not,
  Repository,
  FindManyOptions,
  FindOptionsWhere,
  IsNull
} from 'typeorm'
import { join } from 'node:path'
import { ensureDirSync } from 'fs-extra'
import { isString } from 'lodash'
import { consoleLogger } from '../utils/consoleLogger'

import type { LogFunctions } from 'electron-log'
import type {
  ServerInterface,
  UserQueryCriteria,
  UserLoginParams,
  UserLoginResult,
  ResetUserPasswordParams,
  UpdateUserPasswordParams,
  BaseResult,
  PatientQueryCriteria
} from './types'
import Basic from './entities/basic.entity'
import Patient from './entities/patient.entity'
import User, { UserRole } from './entities/user.entity'
import PatientCase from './entities/patient-case.entity'
import { getDrives } from '../utils/diskinfo'

import fs from 'node:fs'
import path from 'node:path'

// 预定义的一些方法
const dataInterface: ServerInterface = {
  // 关闭数据库
  close,
  // 软件基础信息
  getBasicInfo,
  initBasicInfo,
  updateBasicInfo,
  checkCasePath,
  // 病人信息
  queryPatients,
  createPatient,
  updatePatient,
  deletePatient,
  // 病历信息
  queryPatientCases,
  createPatientCase,
  updatePatientCase,
  deletePatientCase,
  // 用户接口,
  queryUsers,
  createUser,
  updateUser,
  deleteUser,
  userLogin,
  resetUserPassword,
  updateUserPassword,

  // 反向同步病历信息
  // syncStoragePatients,
  // Server-only
  initialize
}

export default dataInterface

let globalInstance: DataSource | undefined
let logger = consoleLogger
let databaseFilePath: string | undefined
let basicRepository: Repository<Basic>
let patientRepository: Repository<Patient>
let userRepository: Repository<User>
let patientCaseRepository: Repository<PatientCase>

// 主进程通知子线程要调用的初始化方法其实就是这个
async function initialize({
  configDir,
  logger: suppliedLogger
}: {
  configDir: string
  logger: Omit<LogFunctions, 'log'>
}): Promise<void> {
  if (globalInstance) throw new Error('Cannot initialize more than once!')

  if (!isString(configDir))
    throw new Error('initialize: configDir is required!')

  logger = suppliedLogger

  const dbDir = join(configDir, 'db')
  // https://github.com/isaacs/node-mkdirp#methods
  ensureDirSync(dbDir, { mode: 0o777 })

  databaseFilePath = join(dbDir, 'database.sqlite')

  logger.info(databaseFilePath)

  let db: DataSource | undefined

  try {
    db = new DataSource({
      type: 'better-sqlite3', // 设定链接的数据库类型
      database: databaseFilePath, // 数据库存放地址
      synchronize: true, // 确保每次运行应用程序时实体都将与数据库同步
      logging: ['error', 'warn'], // 日志，默认在控制台中打印，数组列举错误类型枚举
      entities: [Basic, Patient, User, PatientCase] // 实体或模型表
    })

    // 初始化数据库连接
    if (!db.isInitialized) {
      await db.initialize()
      initRepository(db)
      await initBasicInfo()
      await initAdminUser()
      // await mockUserData()
      // await mockPatientAndCaseData()
    }

    globalInstance = db
  } catch (error) {
    logger.error('Database startup error:', error.stack)

    if (db) db.destroy()

    throw error
  }
}

// 下面是具体方法的实现
async function close(): Promise<void> {
  if (globalInstance) {
    globalInstance.destroy()
  }

  globalInstance = undefined
}

export function getInstance(): DataSource {
  if (!globalInstance) {
    throw new Error('getInstance: globalInstance not set!')
  }

  return globalInstance
}

function initRepository(datasource: DataSource) {
  basicRepository = datasource.getRepository(Basic)
  patientRepository = datasource.getRepository(Patient)
  userRepository = datasource.getRepository(User)
  patientCaseRepository = datasource.getRepository(PatientCase)
}

// 软件基础信息相关
const BASIC_ID_KEY = 1
async function getBasicInfo() {
  return await basicRepository.findOneBy({ id: BASIC_ID_KEY })
}

async function initBasicInfo() {
  const existBasic = await getBasicInfo()
  if (existBasic) return existBasic

  const basic = await basicRepository.create()
  basic.id = BASIC_ID_KEY
  basic.companyName = '江苏诺万医疗设备有限公司'
  basic.saleTel = '0516-87980008'
  basic.afterSaleTel = '0516-87980000'
  basic.website = 'www.nuowan.com'
  basic.touchScreenVersion = 'V1.0.0'
  basic.touchScreenVersionName = '微波治疗'
  basic.mainControlBoardVersion = ''
  basic.instrumentModel = ''
  basic.hospitalName = ''

  return await basicRepository.insert(basic)
}

async function checkCasePath() {
  const existBasic = await getBasicInfo()

  if (!existBasic.casePath) {
    getDrives((err, drives: any[]) => {
      if (!err) {
        if (drives && drives.length) {
          drives.sort((a, b) => b.available - a.available)
          const drive = drives[0]
          const casePath = `${drive.mounted}\\病历\\`
          if (!fs.existsSync(casePath)) {
            fs.mkdirSync(casePath)
          }
          existBasic.casePath = casePath
          updateBasicInfo(existBasic)
        }
      }
    })
  } else {
    // TODO: 若病历文件夹被删除了，则重新创建
    const casePath = existBasic.casePath
    if (!fs.existsSync(casePath)) {
      fs.mkdirSync(casePath)
    }
  }
}

async function updateBasicInfo(basic: Basic) {
  if (basic.id !== BASIC_ID_KEY) basic.id = BASIC_ID_KEY
  return basicRepository.save(basic)
}

// 病人信息管理
async function queryPatients({ keyword, userId, role }: PatientQueryCriteria) {
  let options: FindManyOptions<Patient> | undefined = undefined
  if (keyword) {
    options = {
      where: [
        {
          name: Like(`%${keyword}%`),
          creatorId: role === UserRole.USER ? userId : undefined
        },
        {
          sn: Like(`%${keyword}%`),
          creatorId: role === UserRole.USER ? userId : undefined
        }
      ],
      relations: []
    }
  } else {
    options = {
      where: {
        creatorId: role === UserRole.USER ? userId : undefined
      },
      relations: []
    }
  }

  return await patientRepository.findAndCount(options)
}

async function createPatient(patient: Patient) {
  console.log('createPatient', patient)
  const existPatient = await patientRepository.findOne({
    where: {
      sn: patient.sn
    }
  })
  if (existPatient) {
    const user = await userRepository.findOne({
      where: { id: existPatient.creatorId }
    })
    return {
      success: false,
      message: `当前患者在账号 ${user.loginName} 下已存在，请切换账号后再次进行操作`
    }
  }

  const res = await patientRepository.save(patient)
  return { success: true, message: '创建成功', data: res }
}

async function updatePatient(patient: Patient) {
  const oldPatient = await patientRepository.findOne({
    where: {
      id: patient.id
    }
  })
  const user = await userRepository.findOne({
    where: { id: oldPatient.creatorId }
  })

  if (oldPatient.sn !== patient.sn) {
    // 校验新的sn是否已存在
    const existPatient = await patientRepository.findOne({
      where: {
        sn: patient.sn
      }
    })
    if (existPatient) {
      const user = await userRepository.findOne({
        where: { id: existPatient.creatorId }
      })
      return {
        success: false,
        message: `病历编号在账号 ${user.loginName} 下已存在，请重新输入`
      }
    }

    const basicInfo = await getBasicInfo()
    rmPatientFile(
      path.join(basicInfo.casePath, user.loginName),
      oldPatient.name,
      oldPatient.sn
    )
  }
  const res = await patientRepository.save(patient)

  return { success: true, message: '更新成功', data: res }
}

async function deletePatient(patientId: number) {
  return await patientRepository.softDelete(patientId)
}

async function rmPatientFile(casePath: string, name: string, sn: string) {
  const pdfPath = path.join(casePath, `${name}(${sn}).pdf`)
  const excelPath = path.join(casePath, `${name}(${sn}).xlsx`)
  const pdfExist = fs.existsSync(pdfPath)
  if (pdfExist) {
    fs.rmSync(pdfPath)
  }
  const excelExist = fs.existsSync(excelPath)
  if (excelExist) {
    fs.rmSync(excelPath)
  }
}

/********** 用户接口 **********/
async function getAdminUser() {
  return await userRepository.findOneBy({ loginName: 'admin' })
}
async function getSuperAdminUser() {
  return await userRepository.findOneBy({ loginName: 'NOVA' })
}
async function initAdminUser() {
  const existSuperAdminUser = await getSuperAdminUser()
  if (!existSuperAdminUser) {
    const superAdminUser = await userRepository.create()
    superAdminUser.loginName = 'NOVA'
    superAdminUser.name = '超级管理员'
    superAdminUser.password = 'nuowan'
    superAdminUser.role = UserRole.SUPER
    superAdminUser.latestLoginTime = new Date()
    await userRepository.insert(superAdminUser)
  }

  const existAdminUser = await getAdminUser()
  if (!existAdminUser) {
    const adminUser = await userRepository.create()
    adminUser.loginName = 'admin'
    adminUser.name = '管理员'
    adminUser.password = '123456'
    adminUser.role = UserRole.ADMIN
    adminUser.latestLoginTime = new Date()
    await userRepository.insert(adminUser)
  }
}

// 仅供测试使用
async function mockUserData() {
  const [_, total] = await userRepository.findAndCount({
    where: { deletedTime: null, role: UserRole.USER }
  })
  console.log('mock user before , total : ', total)

  if (total === 0) {
    for (let index = 0; index < 45; index++) {
      const user = await userRepository.create()
      user.loginName = `user${index + 1}`
      user.role = UserRole.USER
      user.name = `用户${index + 1}`
      user.password = '123456'
      await userRepository.insert(user)
    }
    console.log('mock user success !!!!!!!!!!!!!')
  }
}

// 用户信息管理
// 查询用户
async function queryUsers({
  keyword,
  role,
  order,
  isSuper
}: UserQueryCriteria) {
  const where: FindOptionsWhere<User> = {}
  console.log('user keyword : ', keyword, isSuper)
  if (keyword) {
    where.loginName = Like(`%${keyword}%`)
    // where.name = Like(`%${keyword}%`)
  }
  if (role) {
    if (role === UserRole.ADMIN && isSuper) {
      where.role = Not(UserRole.USER)
    } else {
      where.role = role
    }
  } else {
    if (!isSuper) where.role = Not(UserRole.SUPER)
  }

  return await userRepository.find({ where, order })
}
// 新增用户
async function createUser(user: User) {
  const existUser = await userRepository.findOne({
    where: {
      loginName: user.loginName
    }
  })
  if (existUser) return { success: false, message: '用户名已存在，请重新输入' }
  await userRepository.insert(user)
  return { success: true, message: '新增成功' }
}

// 修改用户信息
async function updateUser(user: User) {
  const existUser = await userRepository.findOne({
    where: { loginName: user.loginName }
  })
  if (existUser) return { success: false, message: '用户名重复' }
  await userRepository.save(user)
  return { success: true, message: '修改成功' }
}

// 逻辑删除用户
async function deleteUser(userId: number) {
  const res = await userRepository.softDelete(userId)
  console.log('delete user : ', res, res.raw)
  if (res.affected) {
    const user = await userRepository.findOne({
      where: { id: userId, deletedTime: Not(IsNull()) },
      withDeleted: true
    })
    if (user) {
      console.log('deleted user : ', user)
      const basic = await getBasicInfo()
      if (basic) {
        console.log(
          'need delete folder path : ',
          path.join(basic.casePath, user.loginName)
        )
        await deleteFolderRecursive(path.join(basic.casePath, user.loginName))
        console.log('delete user folder success ~')
      }
    }
  }
  return res
}

// 递归删除文件夹
function deleteFolderRecursive(folderPath) {
  if (fs.existsSync(folderPath)) {
    fs.readdirSync(folderPath).forEach(file => {
      const curPath = path.join(folderPath, file)
      if (fs.lstatSync(curPath).isDirectory()) {
        // 递归删除子文件夹
        deleteFolderRecursive(curPath)
      } else {
        // 删除文件
        fs.unlinkSync(curPath)
      }
    })
    // 删除空文件夹
    fs.rmdirSync(folderPath)
  }
}

// 用户修改密码
async function updateUserPassword({
  userId,
  currentPassword,
  newPassword
}: UpdateUserPasswordParams): Promise<BaseResult> {
  const user = await userRepository.findOne({ where: { id: userId } })
  if (!user) return { success: false, message: '用户不存在' }

  const isMatch = currentPassword === user.password
  if (!isMatch) return { success: false, message: '密码错误' }

  user.password = newPassword
  await userRepository.save(user)
  return { success: true, message: '修改密码成功' }
}

async function resetUserPassword({
  userId,
  newPassword
}: ResetUserPasswordParams) {
  const user = await userRepository.findOne({ where: { id: userId } })
  if (!user) return { success: false, message: '用户不存在' }
  user.password = newPassword
  await userRepository.save(user)
  return { success: true, message: '重置密码成功' }
}

// 用户登录
async function userLogin({
  loginName,
  password
}: UserLoginParams): Promise<UserLoginResult> {
  const user = await userRepository.findOne({
    where: {
      loginName
    }
  })
  if (!user) return { success: false, message: '用户不存在' }
  if (user.password !== password) return { success: false, message: '密码错误' }

  user.latestLoginTime = new Date()
  await userRepository.save(user)

  return { success: true, message: '登录成功', user }
}

// 病人病历新增
async function createPatientCase(patientId: number, patientCase: PatientCase) {
  const patient = await patientRepository.findOne({ where: { id: patientId } })
  if (!patient) return { success: false, message: '病人不存在' }
  patientCase.patient = patient
  await patientCaseRepository.insert(patientCase)
  return { success: true, message: '新建病历信息成功！' }
}

// 病人病历更新
async function updatePatientCase(patientId: number, patientCase: PatientCase) {
  const patient = await patientRepository.findOne({ where: { id: patientId } })
  if (!patient) return { success: false, message: '病人不存在' }
  patientCase.patient = patient
  await patientCaseRepository.save(patientCase)
  return { success: true, message: '更新病历信息成功！' }
}

// 病人病历逻辑删除
async function deletePatientCase(patientCaseId: number) {
  return await patientCaseRepository.softDelete(patientCaseId)
}

async function queryPatientCases(patientId: number) {
  // return await patientRepository.find({
  //   where: { id: patientId },
  //   relations: ['cases']
  // })
  return await patientCaseRepository.find({
    where: { patient: { id: patientId } },
    order: {
      createdTime: 'DESC'
    }
  })
}

async function syncPatientArchives(patientId: number) {
  const cases = patientCaseRepository.find
}
