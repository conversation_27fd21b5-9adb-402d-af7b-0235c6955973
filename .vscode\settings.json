{"typescript.tsdk": "node_modules/typescript/lib", "typescript.tsc.autoDetect": "off", "json.schemas": [{"fileMatch": ["/*electron-builder.json5", "/*electron-builder.json"], "url": "https://json.schemastore.org/electron-builder"}], "cSpell.ignoreWords": ["unref", "vueuse", "pinia", "echarts", "codkd", "nuowan", "ctenhd", "butbacd", "bakk", "YYMMDDH", "Hmmss", "woeclwt", "txhn", "backnbh", "linht", "typeorm", "minimizable", "maximizable", "unplugin"]}