<template>
  <el-dialog v-model="visible" :title="title" fullscreen destroy-on-close :close-on-click-modal="false"
    :close-on-press-escape="false" :before-close="reset">
    <div class="h-[800px] overflow-auto pr-4">
      <el-divider content-position="left">治疗参数</el-divider>
      <el-descriptions :column="5" border>
        <el-descriptions-item label="时间">
          <span class="flex items-center">
            <el-icon size="16px" class="mr-2">
              <Calendar />
            </el-icon>
            <span>{{ cureParams.time }}</span>
          </span>
        </el-descriptions-item>
        <el-descriptions-item label="辐射器">
          <span class="flex items-center">
            <el-icon size="16px" class="mr-2">
              <Document />
            </el-icon>
            <span>{{ cureParams.radiator }}</span>
          </span>
        </el-descriptions-item>
        <el-descriptions-item />
        <el-descriptions-item />
        <el-descriptions-item />
      </el-descriptions>
      <el-divider content-position="left">功率温度曲线</el-divider>
      <div class="flex justify-center">
        <RealtimePowerChart v-if="showChart" ref="realtimeChartRef" :data="cureParams.realtimeChartData!"
          :time="cureParams.time" class="w-[901px] h-[333px]" />
      </div>
      <el-divider content-position="left">治疗记录</el-divider>
      <el-form :model="form" ref="formRef" label-width="48px">
        <el-form-item prop="detail">
          <el-input v-model="form.detail" type="textarea" :autosize="{ minRows: 6, maxRows: 10 }" clearable
            class="flex-1" />
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="close">取消</el-button>
        <el-button type="primary" @click="submit">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import type { ICureParams } from '@/types/medical'
import { ref, reactive, watch, toRaw, nextTick, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Calendar, Document } from '@element-plus/icons-vue'
import RealtimePowerChart from '@/views/action/cure/components/RealtimePowerChart.vue'
import { useUserStore } from '@/store/modules/user'
import { useMedicalStore } from '@/store/modules/medical'
import { type PatientCase, type Patient } from '@/types/entity';
import { computed } from '@vue/reactivity'

defineOptions({
  name: 'CreatePatientCaseModal'
})
const props = defineProps<{
  patient: Patient
}>()
const emit = defineEmits(['close'])
const visible = ref(false)
const formRef = ref<any>(null)
const userStore = useUserStore()
const medicalStore = useMedicalStore()
const currentCase = ref<PatientCase | null>(null)
const realtimeChartRef = ref()
const showChart = ref(false)
// onMounted(() => {
//   nextTick(() => {
//     showChart.value = true
//   })
// })
const INITIAL_CHART_DATA = { power: [], temperatureOne: [], temperatureTwo: [], temperatureThree: [], temperatureFour: [] }
const title = computed(() => currentCase.value ? '编辑病历' : '新建病历')


const cureParams = reactive<ICureParams>({
  time: '',
  radiator: '',
  realtimeChartData: INITIAL_CHART_DATA
})

watch(visible, val => {
  if (!val) {
    cureParams.time = ''
    cureParams.radiator = ''
    cureParams.realtimeChartData = INITIAL_CHART_DATA
  }
})

const form = reactive({
  detail: ''
})

// const rules = {
//   detail: [
//     { required: true, message: '请填写治疗记录', trigger: 'blur' },
//   ]
// }

const reset = (done: () => void) => {
  form.detail = ''
  formRef.value.resetFields()
  done()
}

const close = () => {
  visible.value = false
  form.detail = ''
  formRef.value.resetFields()
}

const submit = async () => {
  if (!formRef.value) return

  try {
    const valid = await formRef.value.validate()
    console.log('valid : ', valid)
    if (valid) {
      const patientCase = {
        detail: form.detail,
        chart: realtimeChartRef.value?.getDataURL() || '',
        cureParams: JSON.stringify(toRaw(cureParams)),
        hospitalName: userStore.getHospitalName,
        creator: userStore.getLoginName,
        creatorId: userStore.getUserId
      }
      // TODO: 在这里处理表单提交逻辑，例如发送API请求
      let res: any;
      if (currentCase.value) {
        // 编辑病历
        res = await window.electronAPI.sqlite.updatePatientCase(props.patient.id, { ...currentCase.value, detail: form.detail } as any)
      } else {
        // 新建
        res = await window.electronAPI.sqlite.createPatientCase(props.patient.id, patientCase as any)
      }
      if (res.success) {
        ElMessage.success(res.message || '新建病历成功')
        emit('close')
        close()
      } else {
        ElMessage.error(res.message || '新建病历失败')
      }
    }
  } catch (error) {
    console.error('表单验证失败', error)
    ElMessage.error('请填写所有必填字段')
  }
}

// 暴露方法以便父组件可以打开弹窗
const open = (row?: PatientCase) => {
  if (row) {
    currentCase.value = row
    form.detail = row.detail
  } else {
    form.detail = ''
    currentCase.value = null
  }
  visible.value = true
  showChart.value = false
  nextTick(() => {
    if (row) {
      console.log('row data : ', row)
      const params = row.cureParams ? JSON.parse(row.cureParams) : {}
      cureParams.time = params.time
      cureParams.radiator = params.radiator
      cureParams.realtimeChartData = params.realtimeChartData ?? INITIAL_CHART_DATA
      form.detail = row.detail
    } else {
      const params = medicalStore.getCureParams
      if (params) {
        cureParams.time = params.time
        cureParams.radiator = params.radiator
        cureParams.realtimeChartData = params.realtimeChartData
      }
      console.log('cureParams : ', cureParams, cureParams.realtimeChartData)
    }
    showChart.value = true
  })
}

// 暴露方法给父组件
defineExpose({
  open
})
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style>
