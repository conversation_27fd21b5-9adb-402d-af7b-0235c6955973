<script setup lang="ts">
import { ref, reactive, onMounted, toRaw } from 'vue'
import PageWrapper from '@/components/PageWrapper/index.vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/store/modules/user';

defineOptions({
  name: 'SuperAdminLoginPage'
})

const router = useRouter()
const userStore = useUserStore()

const loginForm = ref()
const loading = ref(false)

const loginData = reactive({
  loginName: '',
  password: ''
})

const rules = {
  loginName: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度应在3到20个字符之间', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 16, message: '密码长度应在6到16个字符之间', trigger: 'blur' }
  ]
}

const handleSubmit = () => {
  loginForm.value?.validate(async (valid: boolean) => {
    if (valid) {
      loading.value = true
      // 这里应该调用您的登录API
      console.log('登录数据:', loginData)
      const { success, message, user } = await window.electronAPI.sqlite.userLogin(toRaw(loginData))
      loading.value = false
      ElMessage({
        type: success ? 'success' : 'error',
        message
      })
      if (success && user) {
        userStore.setUserData(user)
        router.push({ path: '/home' })
      }
    } else {
      ElMessage.error('请正确填写登录信息')
      return false
    }
  })
}

function handleLogout() {
  console.log('handle logout ~~')
  // window.electronAPI.serialport.quitApp()
  router.replace({ path: '/login' })
}
</script>

<template>
  <PageWrapper show-logo logoutText="返回登录" :logoutAutoBack="false" @logout="handleLogout">
    <div class="content-wrapper  pt-12 pb-8 col-flex justify-evenly items-center text-white">
      <h2 class="mb-12 text-5xl font-bold text-black">超级管理员登录</h2>
      <div class="lg:p-8">
        <div class="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]">
          <el-form ref="loginForm" :model="loginData" :rules="rules" size="large" label-position="top"
            @submit.prevent="handleSubmit">
            <el-form-item label="用户名" prop="loginName">
              <el-input v-model="loginData.loginName" filterable clearable placeholder="请输入" class="w-full">
              </el-input>
            </el-form-item>
            <el-form-item label="密码" prop="password">
              <el-input v-model="loginData.password" type="password" placeholder="请输入密码" show-password
                clearable></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" native-type="submit" :loading="loading" class="login-button">
                登录
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
  </PageWrapper>
</template>
<style scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #f0f2f5;
}

.login-card {
  width: 100%;
  max-width: 400px;
}

.login-title {
  text-align: center;
  color: #409EFF;
}

.login-button {
  width: 100%;
  margin-top: 8px;
}
</style>
