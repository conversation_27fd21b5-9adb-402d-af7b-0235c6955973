import { defineStore } from 'pinia'
import { store } from '@/store'

import type { ICureParams, IPrintParams } from '@/types/medical'
import type { IMedicalRecordState } from '../types'
import { get } from 'lodash'

export const useMedicalStore = defineStore({
  id: 'medical-record',
  state: (): IMedicalRecordState => ({
    cureParams: null,
    pdfContent: null,
    printParams: null,
    sensorFault: false,
    communicationFault: false,
    currentPatientId: null
  }),
  getters: {
    getCureParams(state): ICureParams | null {
      return state.cureParams
    },
    getPdfContent(state): string | null {
      return state.pdfContent
    },
    getPrintParams(state): IPrintParams | null {
      return state.printParams
    },
    getSensorFault(state): boolean {
      return state.sensorFault
    },
    getCommunicationFault(state): boolean {
      return state.communicationFault
    },
    getCurrentPatientId(state): number | null {
      return state.currentPatientId
    }
  },
  actions: {
    setCureParams(data: ICureParams | null) {
      this.cureParams = data
    },
    setPdfContent(data: string | null) {
      this.pdfContent = data
    },
    setPrintParams(data: IPrintParams | null) {
      this.printParams = data
    },
    resetCureParams() {
      this.cureParams = null
    },
    setSensorFault(fault: boolean) {
      this.sensorFault = fault
    },
    setCommunicationFault(fault: boolean) {
      this.communicationFault = fault
    },
    resetFaultState() {
      this.sensorFault = false
      this.communicationFault = false
    },
    setCurrentPatientId(id: number | null) {
      this.currentPatientId = id
    }
  }
})

export function useMedicalStoreHook() {
  return useMedicalStore(store)
}
