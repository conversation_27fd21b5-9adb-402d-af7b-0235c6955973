import { ipcMain } from 'electron'

type SqliteType = {
  sqliteCall(callName: string, args: ReadonlyArray<unknown>): unknown
}

let sqlite: SqliteType | undefined

let initialized = false

const SQLITE_CHANNEL_KEY = 'sqlite-channel'

export function initialize(mainSqlite: SqliteType): void {
  if (initialized) {
    throw new Error('sqlite channels: already initialized!')
  }
  initialized = true

  sqlite = mainSqlite

  // 注册监听事件
  ipcMain.handle(SQLITE_CHANNEL_KEY, async (_event, callName, ...args) => {
    if (!sqlite) {
      throw new Error(`${SQLITE_CHANNEL_KEY}: Not yet initialized!`)
    }
    return sqlite.sqliteCall(callName, args)
  })
}
