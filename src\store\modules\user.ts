import { defineStore } from 'pinia'
import { store } from '@/store'

import type { IUserState } from '../types'
import { UserRole } from '@/types/entity'

export const useUserStore = defineStore({
  id: 'user-store',
  state: (): IUserState => ({
    id: null,
    loginName: null,
    name: null,
    role: null,
    casePath: '',
    hospitalName: ''
  }),
  getters: {
    getUserId(state): number | null {
      return state.id
    },
    getLoginName(state): string | null {
      return state.loginName
    },
    getUserRole(state): UserRole | null {
      return state.role
    },
    getCasePath(state): string {
      return state.casePath
    },
    getHospitalName(state): string {
      return state.hospitalName || ''
    }
  },
  actions: {
    setLoginName(loginName: string) {
      this.loginName = loginName
    },
    setUserData(user: Omit<IUserState, 'casePath'>) {
      this.id = user.id
      this.loginName = user.loginName
      this.name = user.name
      this.role = user.role
    },
    setCasePath(path: string) {
      this.casePath = path
    },
    setHospitalName(name: string) {
      this.hospitalName = name
    },
    logout() {
      this.id = null
      this.loginName = null
      this.name = null
      this.role = null
    }
  }
})

export function useUserStoreHook() {
  return useUserStore(store)
}
