import { StartingUp } from '../instructions/staring-up'
import { ActionChoose } from '../instructions/action-choose'
import { PasswordValidate } from '../instructions/password-validate'
import { WaveformSelection } from '../instructions/wave-selection'
import { IntroChooseSend } from '../instructions/intro-choose'
import { PrescriptionSend } from '../instructions/prescription'
import {
  TemperatureCalibrationChooseSend,
  TemperatureCalibrationPageOneSend,
  TemperatureCalibrationPageTwoSend,
  TemperatureCalibrationPageThreeSend,
  TemperatureCalibrationPageFourSend
} from '../instructions/temperature-calibration'
import { PowerCalibrationPageOneSend } from '../instructions/power-calibration'
import { WavePage } from '../instructions/wave-page'

import { Methods } from './types'

export function generateWriteData(method: Methods, data?: number[]): number[] {
  switch (method) {
    // 开机界面点击操作按钮
    case 'StartingUp.Action':
      return StartingUp.Action
    // 介绍页面点击右上
    case 'IntroChooseSend.TopRight':
      return IntroChooseSend.TopRight
    // 介绍页面点击左上
    case 'IntroChooseSend.TopLeft':
      return IntroChooseSend.TopLeft
    // 介绍页面点击左下
    case 'IntroChooseSend.BottomLeft':
      return IntroChooseSend.BottomLeft
    // 进入微波治疗输入密码后确认
    case 'PasswordValidate.Confirm':
      return PasswordValidate.Confirm
    // 进入微波治疗选择界面
    case 'ActionChoose.MicrowaveCure':
      return ActionChoose.MicrowaveCure
    // 进入温度校准选择界面
    case 'ActionChoose.CorrectTemperature':
      return ActionChoose.CorrectTemperature
    // 进入脉冲波界面
    case 'WaveformSelection.Pulse':
      return WaveformSelection.Pulse
    // 进入三角波界面
    case 'WaveformSelection.Triangle':
      return WaveformSelection.Triangle
    // 进入正弦波界面
    case 'WaveformSelection.Sine':
      return WaveformSelection.Sine
    // 进入连续波界面
    case 'WaveformSelection.Continuation':
      return WaveformSelection.Continuation
    // 波形选择界面点击退出
    case 'WaveformSelection.Exit':
      return WaveformSelection.Exit
    // 处方选择 - 处方1
    case 'PrescriptionSend.One':
      return PrescriptionSend.One
    // 处方选择 - 处方2
    case 'PrescriptionSend.Two':
      return PrescriptionSend.Two
    // 处方选择 - 处方3
    case 'PrescriptionSend.Three':
      return PrescriptionSend.Three
    // 处方选择 - 处方4
    case 'PrescriptionSend.Four':
      return PrescriptionSend.Four
    // 处方选择 - 处方5
    case 'PrescriptionSend.Five':
      return PrescriptionSend.Five
    // 处方选择 - 处方6
    case 'PrescriptionSend.Six':
      return PrescriptionSend.Six
    // 处方选择 - 处方7
    case 'PrescriptionSend.Seven':
      return PrescriptionSend.Seven
    // 处方选择 - 处方8
    case 'PrescriptionSend.Eight':
      return PrescriptionSend.Eight
    // 处方选择 - 处方9
    case 'PrescriptionSend.Nine':
      return PrescriptionSend.Nine
    // 处方选择 - 处方10
    case 'PrescriptionSend.Ten':
      return PrescriptionSend.Ten
    // 处方选择 - 处方11
    case 'PrescriptionSend.Eleven':
      return PrescriptionSend.Eleven
    // 处方选择 - 处方12
    case 'PrescriptionSend.Twelve':
      return PrescriptionSend.Twelve
    // 温度校准选择界面 - T1校准
    case 'TemperatureCalibrationChooseSend.One':
      return TemperatureCalibrationChooseSend.One
    // 温度校准选择界面 - T2校准
    case 'TemperatureCalibrationChooseSend.Two':
      return TemperatureCalibrationChooseSend.Two
    // 温度校准选择界面 - 退出
    case 'TemperatureCalibrationChooseSend.Exit':
      return TemperatureCalibrationChooseSend.Exit
    // 功率校准界面 - 1w
    case 'PowerCalibrationPageOneSend.One':
      return PowerCalibrationPageOneSend.One
    // 功率校准界面 - 5w
    case 'PowerCalibrationPageOneSend.Five':
      return PowerCalibrationPageOneSend.Five
    // 功率校准界面 - 10w
    case 'PowerCalibrationPageOneSend.Ten':
      return PowerCalibrationPageOneSend.Ten
    // 功率校准界面 - 20w
    case 'PowerCalibrationPageOneSend.Twenty':
      return PowerCalibrationPageOneSend.Twenty
    // 功率校准界面 - 40w
    case 'PowerCalibrationPageOneSend.Forty':
      return PowerCalibrationPageOneSend.Forty
    // 功率校准界面 - 60w
    case 'PowerCalibrationPageOneSend.Sixty':
      return PowerCalibrationPageOneSend.Sixty
    // 功率校准界面 - 100w
    case 'PowerCalibrationPageOneSend.Hundred':
      return PowerCalibrationPageOneSend.Hundred
    // 功率校准界面 - 150w
    case 'PowerCalibrationPageOneSend.OneHundredAndFifty':
      return PowerCalibrationPageOneSend.OneHundredAndFifty
    // 功率校准界面 - 200w
    case 'PowerCalibrationPageOneSend.TwoHundred':
      return PowerCalibrationPageOneSend.TwoHundred
    // 功率校准界面 - 步进切换
    case 'PowerCalibrationPageOneSend.ToggleStepValue':
      return PowerCalibrationPageOneSend.ToggleStepValue
    // 功率校准界面 - 调节上键
    case 'PowerCalibrationPageOneSend.Up':
      return PowerCalibrationPageOneSend.Up
    // 功率校准界面 - 调节下键
    case 'PowerCalibrationPageOneSend.Down':
      return PowerCalibrationPageOneSend.Down
    // 功率校准界面 - 暂停
    case 'PowerCalibrationPageOneSend.Pause':
      return PowerCalibrationPageOneSend.Pause
    // 功率校准界面 - 确定
    case 'PowerCalibrationPageOneSend.Confirm':
      return PowerCalibrationPageOneSend.Confirm
    // 功率校准界面 - 退出
    case 'PowerCalibrationPageOneSend.Exit':
      return PowerCalibrationPageOneSend.Exit
    // T1温度校准 - 低温输入后确定
    case 'TemperatureCalibrationPageOneSend.LowTemperatureConfirm':
      return TemperatureCalibrationPageOneSend.LowTemperatureConfirm.concat(
        data
      )
    // T1温度校准 - 低温校准按键
    case 'TemperatureCalibrationPageOneSend.LowTemperatureSubmit':
      return TemperatureCalibrationPageOneSend.LowTemperatureSubmit
    // T1温度校准 - 中温输入后确定
    case 'TemperatureCalibrationPageOneSend.MidTemperatureConfirm':
      return TemperatureCalibrationPageOneSend.MidTemperatureConfirm.concat(
        data
      )
    // T1温度校准 - 中温校准按键
    case 'TemperatureCalibrationPageOneSend.MidTemperatureSubmit':
      return TemperatureCalibrationPageOneSend.MidTemperatureSubmit
    // T1温度校准 - 高温输入后确定
    case 'TemperatureCalibrationPageOneSend.HighTemperatureConfirm':
      return TemperatureCalibrationPageOneSend.HighTemperatureConfirm.concat(
        data
      )
    // T1温度校准 - 高温校准按键
    case 'TemperatureCalibrationPageOneSend.HighTemperatureSubmit':
      return TemperatureCalibrationPageOneSend.HighTemperatureSubmit
    // T1温度校准 - 保存数据并退出
    case 'TemperatureCalibrationPageOneSend.SaveExit':
      return TemperatureCalibrationPageOneSend.SaveExit
    // T1温度校准 - 不保存数据退出
    case 'TemperatureCalibrationPageOneSend.NotSaveExit':
      return TemperatureCalibrationPageOneSend.NotSaveExit

    // T2温度校准 - 低温输入后确定
    case 'TemperatureCalibrationPageTwoSend.LowTemperatureConfirm':
      return TemperatureCalibrationPageTwoSend.LowTemperatureConfirm.concat(
        data
      )
    // T2温度校准 - 低温校准按键
    case 'TemperatureCalibrationPageTwoSend.LowTemperatureSubmit':
      return TemperatureCalibrationPageTwoSend.LowTemperatureSubmit
    // T2温度校准 - 中温输入后确定
    case 'TemperatureCalibrationPageTwoSend.MidTemperatureConfirm':
      return TemperatureCalibrationPageTwoSend.MidTemperatureConfirm.concat(
        data
      )
    // T2温度校准 - 中温校准按键
    case 'TemperatureCalibrationPageTwoSend.MidTemperatureSubmit':
      return TemperatureCalibrationPageTwoSend.MidTemperatureSubmit
    // T2温度校准 - 高温输入后确定
    case 'TemperatureCalibrationPageTwoSend.HighTemperatureConfirm':
      return TemperatureCalibrationPageTwoSend.HighTemperatureConfirm.concat(
        data
      )
    // T2温度校准 - 高温校准按键
    case 'TemperatureCalibrationPageTwoSend.HighTemperatureSubmit':
      return TemperatureCalibrationPageTwoSend.HighTemperatureSubmit
    // T2温度校准 - 保存数据并退出
    case 'TemperatureCalibrationPageTwoSend.SaveExit':
      return TemperatureCalibrationPageTwoSend.SaveExit
    // T2温度校准 - 不保存数据退出
    case 'TemperatureCalibrationPageTwoSend.NotSaveExit':
      return TemperatureCalibrationPageTwoSend.NotSaveExit
    // 波形界面 - 功率调节上键
    case 'WavePage.PowerUp':
      return WavePage.PowerUp
    // 波形界面 - 功率调节下键
    case 'WavePage.PowerDown':
      return WavePage.PowerDown
    // 波形界面 - 周期调节上键
    case 'WavePage.PeriodUp':
      return WavePage.PeriodUp
    // 波形界面 - 周期调节下键
    case 'WavePage.PeriodDown':
      return WavePage.PeriodDown
    // 波形界面 - 音量调节上键
    case 'WavePage.VolumeUp':
      return WavePage.VolumeUp
    // 波形界面 - 音量调节下键
    case 'WavePage.VolumeDown':
      return WavePage.VolumeDown
    // 波形界面 - 时间调节上键
    case 'WavePage.TimeUp':
      return WavePage.TimeUp
    // 波形界面 - 时间调节下键
    case 'WavePage.TimeDown':
      return WavePage.TimeDown
    // 波形界面 - 占空比调节上键
    case 'WavePage.DutyRatioUp':
      return WavePage.DutyRatioUp
    // 波形界面 - 占空比调节下键
    case 'WavePage.DutyRatioDown':
      return WavePage.DutyRatioDown
    // 波形界面 - 静音/解除静音
    case 'WavePage.Mute':
      return WavePage.Mute
    // 波形界面 - 锁屏/解除锁屏
    case 'WavePage.LockScreen':
      return WavePage.LockScreen
    // 波形界面 - 启动
    case 'WavePage.Boot':
      return WavePage.Boot
    // 波形界面 - 停止
    case 'WavePage.Stop':
      return WavePage.Stop
    // 波形界面 - 选择/取消温度T1
    case 'WavePage.ToggleTemperatureOne':
      return WavePage.ToggleTemperatureOne
    // 波形界面 - 选择/取消温度T2
    case 'WavePage.ToggleTemperatureTwo':
      return WavePage.ToggleTemperatureTwo
    // 波形界面 - 选择/取消辐射器
    case 'WavePage.ToggleRadiator':
      return WavePage.ToggleRadiator
    // 波形界面 - 退出
    case 'WavePage.Exit':
      return WavePage.Exit
    case 'WavePage.SendPageNo':
      return WavePage.SendPageNo.concat(data)
    case 'WavePage.ToggleRealtimePower':
      // 连续波界面 - 切换实时功率
      return WavePage.ToggleRealtimePower
    case 'WavePage.PrescriptionSelection':
      // 连续波界面 - 切换实时功率
      return WavePage.PrescriptionSelection

    /************* 新增指令下发 *************/
    // 温度校准选择界面 - T3校准
    case 'TemperatureCalibrationChooseSend.Three':
      return TemperatureCalibrationChooseSend.Three
    // 温度校准选择界面 - T4校准
    case 'TemperatureCalibrationChooseSend.Four':
      return TemperatureCalibrationChooseSend.Four
    // 波形界面 - 选择/取消温度T3
    case 'WavePage.ToggleTemperatureThree':
      return WavePage.ToggleTemperatureThree
    // 波形界面 - 选择/取消温度T4
    case 'WavePage.ToggleTemperatureFour':
      return WavePage.ToggleTemperatureFour
    // 波形界面 - 设定温度调节上键
    case 'WavePage.TemperatureUp':
      return WavePage.TemperatureUp
    // 波形界面 - 设定温度调节下键
    case 'WavePage.TemperatureDown':
      return WavePage.TemperatureDown
    // 波形界面 - 暂停
    case 'WavePage.Pause':
      return WavePage.Pause

    // T3温度校准 - 低温输入后确定
    case 'TemperatureCalibrationPageThreeSend.LowTemperatureConfirm':
      return TemperatureCalibrationPageThreeSend.LowTemperatureConfirm.concat(
        data
      )
    // T3温度校准 - 低温校准按键
    case 'TemperatureCalibrationPageThreeSend.LowTemperatureSubmit':
      return TemperatureCalibrationPageThreeSend.LowTemperatureSubmit
    // T3温度校准 - 中温输入后确定
    case 'TemperatureCalibrationPageThreeSend.MidTemperatureConfirm':
      return TemperatureCalibrationPageThreeSend.MidTemperatureConfirm.concat(
        data
      )
    // T3温度校准 - 中温校准按键
    case 'TemperatureCalibrationPageThreeSend.MidTemperatureSubmit':
      return TemperatureCalibrationPageThreeSend.MidTemperatureSubmit
    // T3温度校准 - 高温输入后确定
    case 'TemperatureCalibrationPageThreeSend.HighTemperatureConfirm':
      return TemperatureCalibrationPageThreeSend.HighTemperatureConfirm.concat(
        data
      )
    // T3温度校准 - 高温校准按键
    case 'TemperatureCalibrationPageThreeSend.HighTemperatureSubmit':
      return TemperatureCalibrationPageThreeSend.HighTemperatureSubmit
    // T3温度校准 - 保存数据并退出
    case 'TemperatureCalibrationPageThreeSend.SaveExit':
      return TemperatureCalibrationPageThreeSend.SaveExit
    // T3温度校准 - 不保存数据退出
    case 'TemperatureCalibrationPageThreeSend.NotSaveExit':
      return TemperatureCalibrationPageThreeSend.NotSaveExit

    // T4温度校准 - 低温输入后确定
    case 'TemperatureCalibrationPageFourSend.LowTemperatureConfirm':
      return TemperatureCalibrationPageFourSend.LowTemperatureConfirm.concat(
        data
      )
    // T4温度校准 - 低温校准按键
    case 'TemperatureCalibrationPageFourSend.LowTemperatureSubmit':
      return TemperatureCalibrationPageFourSend.LowTemperatureSubmit
    // T4温度校准 - 中温输入后确定
    case 'TemperatureCalibrationPageFourSend.MidTemperatureConfirm':
      return TemperatureCalibrationPageFourSend.MidTemperatureConfirm.concat(
        data
      )
    // T4温度校准 - 中温校准按键
    case 'TemperatureCalibrationPageFourSend.MidTemperatureSubmit':
      return TemperatureCalibrationPageFourSend.MidTemperatureSubmit
    // T4温度校准 - 高温输入后确定
    case 'TemperatureCalibrationPageFourSend.HighTemperatureConfirm':
      return TemperatureCalibrationPageFourSend.HighTemperatureConfirm.concat(
        data
      )
    // T4温度校准 - 高温校准按键
    case 'TemperatureCalibrationPageFourSend.HighTemperatureSubmit':
      return TemperatureCalibrationPageFourSend.HighTemperatureSubmit
    // T4温度校准 - 保存数据并退出
    case 'TemperatureCalibrationPageFourSend.SaveExit':
      return TemperatureCalibrationPageFourSend.SaveExit
    // T4温度校准 - 不保存数据退出
    case 'TemperatureCalibrationPageFourSend.NotSaveExit':
      return TemperatureCalibrationPageFourSend.NotSaveExit
    default:
      break
  }
}
