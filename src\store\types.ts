import type { ICureParams, IPrintParams } from '@/types/medical'
import { UserRole } from '@/types/entity'

export type IMedicalRecordState = {
  cureParams: ICureParams | null
  pdfContent: string | null
  printParams: IPrintParams | null
  sensorFault: boolean
  communicationFault: boolean
  currentPatientId: number | null
}

export type ICalibrationState = {
  firstIn: boolean
}

export type ISerialportState = {
  ready: boolean
  switched: boolean
}

export type IUserState = {
  id: number | null
  loginName: string | null
  name?: string | null
  role: UserRole | null
  casePath: string
  hospitalName?: string
}
