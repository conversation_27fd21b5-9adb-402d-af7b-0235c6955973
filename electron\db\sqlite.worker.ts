import { LogFunctions } from 'electron-log'
import { parentPort } from 'worker_threads'
import db from './server'
import {
  WrappedWorkerRequest,
  WrappedWorkerLogEntry,
  WrappedWorkerResponse
} from './main'

// 次文件必须在 worl_thread 中执行
if (!parentPort) throw new Error('Must run as a worker thread')

const port = parentPort

// 向父进程发送消息 postMessage
function respond(seq: number, error: Error | undefined, response?: unknown) {
  const wrappedResponse: WrappedWorkerResponse = {
    type: 'response',
    seq,
    error: error?.stack,
    response
  }
  port.postMessage(wrappedResponse)
}

// 日志相关
const log = (
  level: WrappedWorkerLogEntry['level'],
  args: Array<unknown>
): void => {
  const wrappedResponse: WrappedWorkerResponse = {
    type: 'log',
    level,
    args
  }

  port.postMessage(wrappedResponse)
}

//  'error' | 'warn' | 'info' | 'verbose' | 'debug' | 'silly';
const logger: Omit<LogFunctions, 'log'> = {
  error(...args: Array<unknown>) {
    log('error', args)
  },
  warn(...args: Array<unknown>) {
    log('warn', args)
  },
  info(...args: Array<unknown>) {
    log('info', args)
  },
  verbose(...args: Array<unknown>) {
    log('verbose', args)
  },
  debug(...args: Array<unknown>) {
    log('debug', args)
  },
  silly(...args: Array<unknown>) {
    log('silly', args)
  }
}

// 子线程监听事件
port.on('message', async ({ seq, request }: WrappedWorkerRequest) => {
  try {
    // 通过 type 判断要执行那种操作

    // 初始化
    if (request.type === 'init') {
      await db.initialize({ ...request.options, logger })

      respond(seq, undefined, undefined)

      return
    }

    // 关闭数据库
    if (request.type === 'close') {
      await db.close()

      respond(seq, undefined, undefined)
      process.exit(0)

      return
    }

    // 执行 sql
    if (request.type === 'sqliteCall') {
      const method = db[request.method]
      if (typeof method !== 'function') {
        throw new Error(`Invalid sql method: ${method}`)
      }

      const start = Date.now()
      const result = await (method as Function).apply(db, request.args)
      const end = Date.now()

      respond(seq, undefined, { result, duration: end - start })

      return
    }

    throw new Error('Unexpected request type')
  } catch (error) {
    respond(seq, error, undefined)
  }
})
