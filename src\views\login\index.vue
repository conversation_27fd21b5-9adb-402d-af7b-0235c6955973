<script setup lang="ts">
import { ref, onMounted, onErrorCaptured } from 'vue'
import { ElMessage } from 'element-plus'
import cornerstone, {
  init as csInit,
  cache,
  Enums as csEnums,
  RenderingEngine,
  getRenderingEngine,
  setVolumesForViewports,
  volumeLoader,
  utilities as csUtilities
} from "@cornerstonejs/core";
import cornerstoneTools, {
  init as cstInit,
  addTool,
  Enums as cstEnums,
  PanTool,
  StackScrollTool,
  ToolGroupManager,
  ZoomTool
} from "@cornerstonejs/tools";
import cornerstoneDICOMImageLoader from "@cornerstonejs/dicom-image-loader";

// Create refs
const elementRef = ref<HTMLDivElement | null>(null)
const fileImageIds = ref<string[]>([]);
const isInitialized = ref(false);
const isDicomLoaderInitialized = ref(false);
const loading = ref(false);

const renderingEngineId = "myRenderingEngine";
const viewportId1 = "CT_AXIAL";
const viewportId2 = "CT_SAGITTAL";
const viewportId3 = "CT_CORONAL";
const volumeId = "CT_VOLUME";
const toolGroupId = "CT_TOOLGROUP";

// 初始化cornerstone库
// 检查 WebGL 支持
function checkWebGLSupport() {
  console.log("=== Checking WebGL Support ===");

  // 检查基本环境
  console.log("User Agent:", navigator.userAgent);
  console.log("Platform:", navigator.platform);
  console.log("Hardware Concurrency:", navigator.hardwareConcurrency);

  try {
    const canvas = document.createElement('canvas');
    console.log("Canvas created successfully");

    // 尝试获取 WebGL 上下文
    let gl = canvas.getContext('webgl') as WebGLRenderingContext;
    console.log("WebGL context attempt:", gl ? "Success" : "Failed");

    if (!gl) {
      console.log("Trying experimental-webgl...");
      gl = canvas.getContext('experimental-webgl') as WebGLRenderingContext;
      console.log("Experimental WebGL context attempt:", gl ? "Success" : "Failed");
    }

    if (gl) {
      console.log("✓ WebGL is supported");
      try {
        console.log("WebGL Vendor:", gl.getParameter(gl.VENDOR));
        console.log("WebGL Renderer:", gl.getParameter(gl.RENDERER));
        console.log("WebGL Version:", gl.getParameter(gl.VERSION));
        console.log("WebGL Shading Language Version:", gl.getParameter(gl.SHADING_LANGUAGE_VERSION));

        // 检查扩展
        const extensions = gl.getSupportedExtensions();
        console.log("WebGL Extensions:", extensions);

        // 检查最大纹理大小
        console.log("Max Texture Size:", gl.getParameter(gl.MAX_TEXTURE_SIZE));
        console.log("Max Viewport Dims:", gl.getParameter(gl.MAX_VIEWPORT_DIMS));

      } catch (paramError) {
        console.error("Error getting WebGL parameters:", paramError);
      }
      return true;
    } else {
      console.error("✗ WebGL is not supported");
      console.error("Possible reasons:");
      console.error("1. Hardware acceleration is disabled");
      console.error("2. Graphics drivers are outdated");
      console.error("3. Browser doesn't support WebGL");
      console.error("4. WebGL is disabled in browser settings");
      return false;
    }
  } catch (e) {
    console.error("✗ WebGL check failed:", e);
    if (e instanceof Error) {
      console.error("Error details:", e.message);
    }
    return false;
  }
}

// 简化的测试函数，逐步测试每个部分
async function testCornerstoneStep1() {
  console.log("=== Testing Step 1: csInit ===");
  try {
    // 先检查 WebGL 支持
    if (!checkWebGLSupport()) {
      throw new Error("WebGL not supported");
    }

    await csInit();
    console.log("✓ csInit successful");
    return true;
  } catch (e) {
    console.error("✗ csInit failed:", e);
    return false;
  }
}

async function testCornerstoneStep2() {
  console.log("=== Testing Step 2: cstInit ===");
  try {
    await cstInit();
    console.log("✓ cstInit successful");
    return true;
  } catch (e) {
    console.error("✗ cstInit failed:", e);
    return false;
  }
}

async function testCornerstoneStep3() {
  console.log("=== Testing Step 3: DICOM loader ===");
  try {
    cornerstoneDICOMImageLoader.init({
      maxWebWorkers: Math.min(navigator.hardwareConcurrency, 4)
    });
    console.log("✓ DICOM loader init successful");
    return true;
  } catch (e) {
    console.error("✗ DICOM loader init failed:", e);
    return false;
  }
}

async function initCornerstone() {
  console.log("=== initCornerstone START ===");

  if (isInitialized.value) {
    console.log("Cornerstone already initialized, skipping...");
    return;
  }

  try {
    // 逐步测试每个部分
    console.log("Testing individual steps...");

    const step1 = await testCornerstoneStep1();
    if (!step1) throw new Error("Step 1 failed");

    const step2 = await testCornerstoneStep2();
    if (!step2) throw new Error("Step 2 failed");

    const step3 = await testCornerstoneStep3();
    if (!step3) throw new Error("Step 3 failed");

    console.log("All basic steps passed, continuing with full initialization...");

    // 4. 注册工具
    console.log("Step 4: Registering tools...");
    addTool(PanTool);
    addTool(ZoomTool);
    addTool(StackScrollTool);
    console.log("Step 4: ✓ All tools registered");

    // 5. 创建工具组
    console.log("Step 5: Creating tool group...");
    const toolGroup = ToolGroupManager.createToolGroup(toolGroupId);
    if (toolGroup) {
      toolGroup.addTool(PanTool.toolName);
      toolGroup.addTool(ZoomTool.toolName);
      toolGroup.addTool(StackScrollTool.toolName);

      toolGroup.setToolActive(PanTool.toolName, {
        bindings: [{ mouseButton: cstEnums.MouseBindings.Primary }]
      });
      toolGroup.setToolActive(ZoomTool.toolName, {
        bindings: [{ mouseButton: cstEnums.MouseBindings.Secondary }]
      });
      toolGroup.setToolActive(StackScrollTool.toolName, {
        bindings: [{ mouseButton: cstEnums.MouseBindings.Wheel }]
      });
    }
    console.log("Step 5: ✓ Tool group created and configured");

    // 6. 创建渲染引擎
    console.log("Step 6: Creating rendering engine...");
    new RenderingEngine(renderingEngineId);
    console.log("Step 6: ✓ Rendering engine created");

    isInitialized.value = true;
    console.log("=== initCornerstone SUCCESS ===");
  } catch (error) {
    console.error("=== initCornerstone FAILED ===");
    console.error("Final error:", error);
    if (error instanceof Error) {
      console.error("Error message:", error.message);
      console.error("Error stack:", error.stack);
    }
    isInitialized.value = false;
    ElMessage.error("初始化失败，请刷新页面重试");
    throw error;
  }
}

async function handleChange(evt: Event) {
  // 阻止事件冒泡
  evt.stopPropagation();
  evt.preventDefault();

  if (!isInitialized.value) {
    ElMessage.warning("系统正在初始化，请稍后再试");
    return;
  }

  // 暂时禁用 DICOM 文件处理，避免崩溃
  ElMessage.warning("DICOM 文件处理功能暂时禁用，正在寻找替代方案");
  return;

  // 以下代码暂时注释，避免崩溃
  /*
  loading.value = true;

  try {
    // 在处理文件前，安全地初始化 DICOM 加载器
    const dicomInitSuccess = await initDicomLoaderSafely();
    if (!dicomInitSuccess) {
      ElMessage.error("DICOM 加载器初始化失败，无法处理文件");
      loading.value = false;
      return;
    }

    // 更新文件前清除之前的缓存
    cache.purgeCache();

    const files: FileList = (evt.target as HTMLInputElement).files!;

    if (files.length === 0) {
      loading.value = false;
      return;
    }

    const imageIds: string[] = [];
    console.log('处理DICOM文件...');

    Array.from(files).forEach(file => {
      const imageId = cornerstoneDICOMImageLoader.wadouri.fileManager.add(file);
      imageIds.push(imageId);
    });

    fileImageIds.value = imageIds;
    await loadAndViewImage(imageIds);
  } catch (error) {
    console.error("处理文件失败:", error);
    ElMessage.error("处理文件失败，请确保选择的是有效的DICOM文件");
    loading.value = false;
  }
  */
}

async function loadAndViewImage(imageIds: string[]) {
  try {
    if (imageIds.length === 0) {
      ElMessage.warning("未选择任何文件");
      loading.value = false;
      return;
    }

    if (imageIds.length < 5) {
      ElMessage.warning("请至少选择5张DICOM图片进行MPR展示");
      loading.value = false;
      return;
    }

    console.log('开始加载元数据...');
    await prefetchMetadataInformation(imageIds);
    console.log('元数据加载完成，开始渲染...');
    await renderVolume(imageIds);
    loading.value = false;
  } catch (error) {
    console.error("加载图像失败:", error);
    ElMessage.error("加载图像失败，请确保选择的是有效的DICOM文件");
    loading.value = false;
  }
}

async function prefetchMetadataInformation(imageIdsToPrefetch: string[]) {
  const loadPromises = imageIdsToPrefetch.map(imageId => {
    return cornerstoneDICOMImageLoader.wadouri.loadImage(imageId).promise;
  });

  try {
    await Promise.all(loadPromises);
  } catch (error) {
    console.error("加载元数据失败:", error);
    throw new Error("加载元数据失败");
  }
}

async function renderVolume(imageIds: string[]) {
  // 在缓存中删除上一次加载的影像
  if (cache.getVolume(volumeId)) {
    cache.removeVolumeLoadObject(volumeId);
  }

  const renderingEngine = new RenderingEngine(renderingEngineId);

  // 确保DOM元素存在
  const element1 = document.getElementById("element1") as HTMLDivElement;
  const element2 = document.getElementById("element2") as HTMLDivElement;
  const element3 = document.getElementById("element3") as HTMLDivElement;

  if (!element1 || !element2 || !element3) {
    throw new Error("找不到渲染容器元素");
  }

  // 设置视口
  const viewportInputArray = [
    {
      viewportId: viewportId1,
      type: csEnums.ViewportType.ORTHOGRAPHIC,
      element: element1,
      defaultOptions: {
        orientation: csEnums.OrientationAxis.AXIAL
      }
    },
    {
      viewportId: viewportId2,
      type: csEnums.ViewportType.ORTHOGRAPHIC,
      element: element2,
      defaultOptions: {
        orientation: csEnums.OrientationAxis.SAGITTAL
      }
    },
    {
      viewportId: viewportId3,
      type: csEnums.ViewportType.ORTHOGRAPHIC,
      element: element3,
      defaultOptions: {
        orientation: csEnums.OrientationAxis.CORONAL
      }
    }
  ];

  renderingEngine.setViewports(viewportInputArray);

  // 将视口添加到工具组
  const toolGroup = ToolGroupManager.getToolGroup(toolGroupId);
  if (toolGroup) {
    toolGroup.addViewport(viewportId1, renderingEngineId);
    toolGroup.addViewport(viewportId2, renderingEngineId);
    toolGroup.addViewport(viewportId3, renderingEngineId);
  }

  try {
    // 创建并加载体积数据
    const volume = await volumeLoader.createAndCacheVolume(volumeId, {
      imageIds
    });

    await volume.load();

    // 设置视口的体积数据
    await setVolumesForViewports(
      renderingEngine,
      [{ volumeId }],
      [viewportId1, viewportId2, viewportId3]
    );

    // 渲染
    renderingEngine.render();

    console.log('渲染完成');
    ElMessage.success("DICOM图像加载成功");
  } catch (error) {
    console.error("渲染体积数据失败:", error);
    throw new Error("渲染体积数据失败");
  }
}

// 全局错误捕获
onErrorCaptured((err, instance, info) => {
  console.error('=== Vue Error Captured ===');
  console.error('Error:', err);
  console.error('Error Message:', err.message);
  console.error('Error Stack:', err.stack);
  console.error('Error Info:', info);
  console.error('Instance:', instance);
  console.error('========================');

  return false; // 继续传播错误
});

// 添加全局未捕获错误监听
window.addEventListener('error', (event) => {
  console.error('=== Global Error ===');
  console.error('Message:', event.message);
  console.error('Filename:', event.filename);
  console.error('Line:', event.lineno);
  console.error('Column:', event.colno);
  console.error('Error:', event.error);
  console.error('Stack:', event.error?.stack);
  console.error('==================');
});

// 添加未捕获的 Promise 错误监听
window.addEventListener('unhandledrejection', (event) => {
  console.error('=== Unhandled Promise Rejection ===');
  console.error('Reason:', event.reason);
  console.error('Promise:', event.promise);
  console.error('================================');
});

// 最小化测试函数
async function minimalTest() {
  console.log("=== Minimal Test START ===");

  try {
    // 只测试 WebGL
    console.log("Testing WebGL...");
    if (!checkWebGLSupport()) {
      throw new Error("WebGL not supported");
    }

    console.log("WebGL test passed, trying basic csInit...");

    // 只初始化核心库，不做其他操作
    await csInit();
    console.log("✓ csInit completed without crash");

    console.log("=== Minimal Test SUCCESS ===");
    return true;
  } catch (e) {
    console.error("=== Minimal Test FAILED ===");
    console.error("Error:", e);
    return false;
  }
}

// 手动测试函数
function testWebGL() {
  console.log("=== Manual WebGL Test ===");
  try {
    const webglSupported = checkWebGLSupport();
    if (webglSupported) {
      ElMessage.success("WebGL 支持正常");
    } else {
      ElMessage.error("WebGL 不支持");
    }
  } catch (e) {
    console.error("WebGL test error:", e);
    ElMessage.error("WebGL 测试出错");
  }
}

async function testMinimal() {
  console.log("=== Manual Minimal Test ===");
  try {
    const result = await minimalTest();
    if (result) {
      ElMessage.success("最小化测试通过");
    } else {
      ElMessage.error("最小化测试失败");
    }
  } catch (e) {
    console.error("Minimal test error:", e);
    ElMessage.error("最小化测试出错");
  }
}

async function testFullInit() {
  console.log("=== Manual Full Initialization ===");
  try {
    await initCornerstoneStepByStep();
    ElMessage.success("完整初始化成功");
  } catch (e) {
    console.error("Full init error:", e);
    ElMessage.error("完整初始化失败");
  }
}

async function testSafeInit() {
  console.log("=== Safe Initialization (Skip Tools) ===");
  try {
    await initCornerstoneSafe();
    ElMessage.success("安全初始化成功");
  } catch (e) {
    console.error("Safe init error:", e);
    ElMessage.error("安全初始化失败");
  }
}

async function testNoDicom() {
  console.log("=== No DICOM Initialization ===");
  try {
    await initCornerstoneNoDicom();
    ElMessage.success("跳过DICOM初始化成功");
  } catch (e) {
    console.error("No DICOM init error:", e);
    ElMessage.error("跳过DICOM初始化失败");
  }
}

async function testDicomLoader() {
  console.log("=== Test DICOM Loader ===");
  try {
    await initDicomLoaderSafely();
  } catch (e) {
    console.error("DICOM loader test error:", e);
    ElMessage.error("DICOM 加载器测试失败");
  }
}

async function testAlternativeLoader() {
  console.log("=== Test Alternative Image Loader ===");
  try {
    // 测试创建一个简单的图像加载器
    console.log("Creating test viewport...");

    const renderingEngine = getRenderingEngine(renderingEngineId);
    if (!renderingEngine) {
      throw new Error("Rendering engine not found");
    }

    console.log("✅ Rendering engine found:", renderingEngine);

    // 测试创建视口
    const element1 = document.getElementById("element1") as HTMLDivElement;
    if (!element1) {
      throw new Error("Viewport element not found");
    }

    console.log("✅ Viewport element found");

    // 创建一个简单的测试视口
    const viewportInputArray = [{
      viewportId: "test-viewport",
      type: csEnums.ViewportType.STACK,
      element: element1,
    }];

    renderingEngine.setViewports(viewportInputArray);
    console.log("✅ Test viewport created successfully");

    ElMessage.success("替代加载器测试成功 - 可以创建视口");

  } catch (e) {
    console.error("Alternative loader test error:", e);
    ElMessage.error("替代加载器测试失败");
  }
}

// 安全的 DICOM 加载器初始化函数
async function initDicomLoaderSafely() {
  if (isDicomLoaderInitialized.value) {
    console.log("DICOM loader already initialized");
    return true;
  }

  // cornerstoneDICOMImageLoader.external.cornerstone = cornerstone
  // cornerstoneDICOMImageLoader.external.dicomParser = dicomParser

  console.log("🔄 Initializing DICOM loader safely...");
  try {
    console.log(`cornerstoneDICOMImageLoader.init : `, cornerstoneDICOMImageLoader.init)
    await cornerstoneDICOMImageLoader.init();
    isDicomLoaderInitialized.value = true;
    console.log("✅ DICOM loader initialized successfully");
    ElMessage.success("DICOM 加载器初始化成功");
    return true;
  } catch (error) {
    console.error("❌ DICOM loader initialization failed:", error);
    ElMessage.error("DICOM 加载器初始化失败");
    return false;
  }
}

// 完全跳过 DICOM 加载器的初始化
async function initCornerstoneNoDicom() {
  console.log("=== No DICOM Cornerstone Initialization ===");

  if (isInitialized.value) {
    console.log("Already initialized, skipping...");
    return;
  }

  try {
    // Step 1: Core initialization
    console.log("🔄 Step 1: Initializing Cornerstone core...");
    await csInit();
    console.log("✅ Step 1: Cornerstone core initialized");
    await new Promise(resolve => setTimeout(resolve, 500));

    // Step 2: Tools initialization
    console.log("🔄 Step 2: Initializing Cornerstone tools...");
    await cstInit();
    console.log("✅ Step 2: Cornerstone tools initialized");
    await new Promise(resolve => setTimeout(resolve, 500));

    // 跳过 DICOM 加载器，直接注册工具
    console.log("🔄 Step 3: Registering tools (no DICOM)...");
    addTool(PanTool);
    addTool(ZoomTool);
    addTool(StackScrollTool);
    console.log("✅ Step 3: Tools registered");
    await new Promise(resolve => setTimeout(resolve, 500));

    // Step 4: 创建工具组
    console.log("🔄 Step 4: Creating tool group...");
    const toolGroup = ToolGroupManager.createToolGroup(toolGroupId);
    if (toolGroup) {
      toolGroup.addTool(PanTool.toolName);
      toolGroup.addTool(ZoomTool.toolName);
      toolGroup.addTool(StackScrollTool.toolName);

      toolGroup.setToolActive(PanTool.toolName, {
        bindings: [{ mouseButton: cstEnums.MouseBindings.Primary }]
      });
      toolGroup.setToolActive(ZoomTool.toolName, {
        bindings: [{ mouseButton: cstEnums.MouseBindings.Secondary }]
      });
      toolGroup.setToolActive(StackScrollTool.toolName, {
        bindings: [{ mouseButton: cstEnums.MouseBindings.Wheel }]
      });
    }
    console.log("✅ Step 4: Tool group created and configured");
    await new Promise(resolve => setTimeout(resolve, 500));

    // Step 5: 创建渲染引擎
    console.log("🔄 Step 5: Creating rendering engine...");
    const renderingEngine = new RenderingEngine(renderingEngineId);
    console.log("✅ Step 5: Rendering engine created successfully (no DICOM)");

    isInitialized.value = true;
    console.log("🎉 === No DICOM Initialization Completed Successfully ===");

  } catch (error) {
    console.error("❌ No DICOM initialization failed:", error);
    if (error instanceof Error) {
      console.error("Error message:", error.message);
      console.error("Error stack:", error.stack);
    }
    isInitialized.value = false;
    throw error;
  }
}

// 安全初始化，跳过工具注册
async function initCornerstoneSafe() {
  console.log("=== Safe Cornerstone Initialization (No Tools) ===");

  if (isInitialized.value) {
    console.log("Already initialized, skipping...");
    return;
  }

  try {
    // Step 1: Core initialization
    console.log("🔄 Step 1: Initializing Cornerstone core...");
    await csInit();
    console.log("✅ Step 1: Cornerstone core initialized");
    await new Promise(resolve => setTimeout(resolve, 500));

    // Step 2: Tools initialization
    console.log("🔄 Step 2: Initializing Cornerstone tools...");
    await cstInit();
    console.log("✅ Step 2: Cornerstone tools initialized");
    await new Promise(resolve => setTimeout(resolve, 500));

    // // Step 3: DICOM loader
    // console.log("🔄 Step 3: Configuring DICOM image loader...");
    // cornerstoneDICOMImageLoader.init({
    //   maxWebWorkers: navigator.hardwareConcurrency || 1,
    //   decodeConfig: {
    //     convertFloatPixelDataToInt: false,
    //   },
    // });
    // console.log("✅ Step 3: DICOM image loader configured");
    // await new Promise(resolve => setTimeout(resolve, 500));

    // // 跳过工具注册，直接创建渲染引擎
    // console.log("🔄 Step 4: Creating rendering engine (skipping tools)...");
    // try {
    //   const renderingEngine = new RenderingEngine(renderingEngineId);
    //   console.log("✅ Step 4: Rendering engine created successfully (no tools)");
    // } catch (renderError) {
    //   console.error("❌ Rendering engine creation failed:", renderError);
    //   throw renderError;
    // }

    isInitialized.value = true;
    console.log("🎉 === Safe Initialization Completed Successfully ===");

  } catch (error) {
    console.error("❌ Safe initialization failed:", error);
    if (error instanceof Error) {
      console.error("Error message:", error.message);
      console.error("Error stack:", error.stack);
    }
    isInitialized.value = false;
    throw error;
  }
}

// 逐步初始化，每步之间有延迟，便于观察哪一步导致问题
async function initCornerstoneStepByStep() {
  console.log("=== Step-by-Step Cornerstone Initialization ===");

  if (isInitialized.value) {
    console.log("Already initialized, skipping...");
    return;
  }

  try {
    // Step 1: Core initialization
    console.log("🔄 Step 1: Initializing Cornerstone core...");
    await csInit();
    console.log("✅ Step 1: Cornerstone core initialized");
    await new Promise(resolve => setTimeout(resolve, 500)); // 延迟观察

    // Step 2: Tools initialization
    console.log("🔄 Step 2: Initializing Cornerstone tools...");
    await cstInit();
    console.log("✅ Step 2: Cornerstone tools initialized");
    await new Promise(resolve => setTimeout(resolve, 500));

    // Step 3: DICOM loader (安全配置)
    console.log("🔄 Step 3: Configuring DICOM image loader...");
    try {
      // 使用更安全的配置
      cornerstoneDICOMImageLoader.init({
        maxWebWorkers: 1, // 减少到1个worker避免内存问题
        decodeConfig: {
          convertFloatPixelDataToInt: false,
          useWebWorkers: true,
        }
      });
      console.log("✅ Step 3: DICOM image loader configured safely");
    } catch (dicomError) {
      console.error("❌ Step 3: DICOM loader configuration failed:", dicomError);
      throw dicomError;
    }
    await new Promise(resolve => setTimeout(resolve, 500));

    // Step 4: Tool registration (细化每个工具的注册)
    console.log("🔄 Step 4: Registering tools...");

    try {
      console.log("🔄 Step 4a: Registering PanTool...");
      addTool(PanTool);
      console.log("✅ Step 4a: PanTool registered successfully");
      await new Promise(resolve => setTimeout(resolve, 500));
    } catch (e) {
      console.error("❌ Step 4a: PanTool registration failed:", e);
      throw e;
    }

    try {
      console.log("🔄 Step 4b: Registering ZoomTool...");
      addTool(ZoomTool);
      console.log("✅ Step 4b: ZoomTool registered successfully");
      await new Promise(resolve => setTimeout(resolve, 500));
    } catch (e) {
      console.error("❌ Step 4b: ZoomTool registration failed:", e);
      throw e;
    }

    try {
      console.log("🔄 Step 4c: Registering StackScrollTool...");
      addTool(StackScrollTool);
      console.log("✅ Step 4c: StackScrollTool registered successfully");
      await new Promise(resolve => setTimeout(resolve, 500));
    } catch (e) {
      console.error("❌ Step 4c: StackScrollTool registration failed:", e);
      throw e;
    }

    console.log("✅ Step 4: All tools registered successfully");

    // Step 5: Tool group creation
    console.log("🔄 Step 5: Creating tool group...");
    const toolGroup = ToolGroupManager.createToolGroup(toolGroupId);
    console.log("✅ Step 5a: Tool group created");
    await new Promise(resolve => setTimeout(resolve, 300));

    if (toolGroup) {
      console.log("🔄 Step 5b: Adding tools to group...");
      toolGroup.addTool(PanTool.toolName);
      toolGroup.addTool(ZoomTool.toolName);
      toolGroup.addTool(StackScrollTool.toolName);
      console.log("✅ Step 5b: Tools added to group");
      await new Promise(resolve => setTimeout(resolve, 300));

      console.log("🔄 Step 5c: Activating tools...");
      toolGroup.setToolActive(PanTool.toolName, {
        bindings: [{ mouseButton: cstEnums.MouseBindings.Primary }]
      });
      console.log("✅ Step 5c1: PanTool activated");
      await new Promise(resolve => setTimeout(resolve, 200));

      toolGroup.setToolActive(ZoomTool.toolName, {
        bindings: [{ mouseButton: cstEnums.MouseBindings.Secondary }]
      });
      console.log("✅ Step 5c2: ZoomTool activated");
      await new Promise(resolve => setTimeout(resolve, 200));

      toolGroup.setToolActive(StackScrollTool.toolName, {
        bindings: [{ mouseButton: cstEnums.MouseBindings.Wheel }]
      });
      console.log("✅ Step 5c3: StackScrollTool activated");
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    // Step 6: Rendering engine (最可能导致问题的步骤)
    console.log("🔄 Step 6: Creating rendering engine...");
    console.log("⚠️  This step might cause DevTools disconnection...");

    // 添加额外的监控
    console.log("Before rendering engine creation - checking WebGL again...");
    if (!checkWebGLSupport()) {
      throw new Error("WebGL support lost before rendering engine creation");
    }

    try {
      console.log("Creating RenderingEngine with ID:", renderingEngineId);
      const renderingEngine = new RenderingEngine(renderingEngineId);
      console.log("✅ Step 6: Rendering engine created successfully");
      console.log("Rendering engine object:", renderingEngine);
      console.log("🎉 If you see this message, rendering engine creation didn't crash!");

      // 验证渲染引擎是否正常工作
      console.log("Verifying rendering engine...");
      const engines = getRenderingEngine(renderingEngineId);
      console.log("Retrieved rendering engine:", engines);

    } catch (renderError) {
      console.error("❌ Rendering engine creation failed:", renderError);
      throw renderError;
    }

    await new Promise(resolve => setTimeout(resolve, 1000));

    isInitialized.value = true;
    console.log("🎉 === Full Initialization Completed Successfully ===");

  } catch (error) {
    console.error("❌ Step-by-step initialization failed:", error);
    if (error instanceof Error) {
      console.error("Error message:", error.message);
      console.error("Error stack:", error.stack);
    }
    isInitialized.value = false;
    throw error;
  }
}

// 组件挂载时初始化cornerstone
onMounted(() => {
  console.log("=== Component Mounted ===");
  console.log("Page loaded successfully. Use buttons to test step by step.");

  setTimeout(() => {
    ElMessage.success("页面加载成功，请使用按钮逐步测试");
  }, 500);
});
</script>

<template>
  <div class="dicom-viewer-container">
    <div class="control-panel">
      <div class="test-buttons">
        <button @click="testWebGL" class="test-btn">测试 WebGL</button>
        <button @click="testMinimal" class="test-btn">最小化测试</button>
        <button @click="testNoDicom" class="test-btn" :disabled="isInitialized">初始化Cornerstone</button>
        <button @click="testAlternativeLoader" class="test-btn" :disabled="!isInitialized">测试替代加载器</button>
        <button @click="testDicomLoader" class="test-btn" :disabled="!isInitialized">测试DICOM加载器(已禁用)</button>
        <span v-if="isInitialized" class="status-text">✓ 已初始化</span>
      </div>
      <input type="file" multiple accept=".dcm" @change="handleChange" :disabled="loading" />
      <div v-if="loading" class="loading-indicator">加载中...</div>
    </div>

    <div class="viewport-container">
      <div id="element1" class="cornerstone-viewport" />
      <div id="element2" class="cornerstone-viewport" />
      <div id="element3" class="cornerstone-viewport" />
    </div>
  </div>
</template>

<style scoped>
.dicom-viewer-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100vh;
  padding: 20px;
  box-sizing: border-box;
}

.control-panel {
  margin-bottom: 20px;
}

.test-buttons {
  margin-bottom: 15px;
  display: flex;
  gap: 10px;
  align-items: center;
}

.test-btn {
  padding: 8px 16px;
  background-color: #409EFF;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.test-btn:hover {
  background-color: #337ecc;
}

.test-btn:disabled {
  background-color: #c0c4cc;
  cursor: not-allowed;
}

.status-text {
  color: #67c23a;
  font-weight: bold;
}

.loading-indicator {
  margin-top: 10px;
  color: #409EFF;
  font-weight: bold;
}

.viewport-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.cornerstone-viewport {
  width: 300px;
  height: 300px;
  border: 2px solid #96CDF2;
  border-radius: 10px;
  background-color: #000;
}
</style>
