export const TemperatureCalibrationChooseSend = {
  One: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x00, 0x01, 0x00, 0x90],
  Two: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x00, 0x01, 0x00, 0x91],
  Three: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x00, 0x01, 0x00, 0x8e],
  Four: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x00, 0x01, 0x00, 0x8f],
  Exit: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x00, 0x01, 0x00, 0x92]
}

export const TemperatureCalibrationPageOneSend = {
  LowTemperatureConfirm: [0x5a, 0xa5, 0x06, 0x83, 0x30, 0x11, 0x01],
  LowTemperatureSubmit: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x00, 0x01, 0x00, 0x81],
  MidTemperatureConfirm: [0x5a, 0xa5, 0x06, 0x83, 0x30, 0x12, 0x01],
  MidTemperatureSubmit: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x00, 0x01, 0x00, 0x82],
  HighTemperatureConfirm: [0x5a, 0xa5, 0x06, 0x83, 0x30, 0x13, 0x01],
  HighTemperatureSubmit: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x00, 0x01, 0x00, 0x83],
  SaveExit: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x00, 0x01, 0x00, 0x84],
  NotSaveExit: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x00, 0x01, 0x00, 0x85]
}

export const TemperatureCalibrationPageTwoSend = {
  LowTemperatureConfirm: [0x5a, 0xa5, 0x06, 0x83, 0x30, 0x01, 0x01],
  LowTemperatureSubmit: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x00, 0x01, 0x00, 0x93],
  MidTemperatureConfirm: [0x5a, 0xa5, 0x06, 0x83, 0x30, 0x02, 0x01],
  MidTemperatureSubmit: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x00, 0x01, 0x00, 0x94],
  HighTemperatureConfirm: [0x5a, 0xa5, 0x06, 0x83, 0x30, 0x03, 0x01],
  HighTemperatureSubmit: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x00, 0x01, 0x00, 0x95],
  SaveExit: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x00, 0x01, 0x00, 0x96],
  NotSaveExit: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x00, 0x01, 0x00, 0x97]
}

export const TemperatureCalibrationPageThreeSend = {
  LowTemperatureConfirm: [0x5a, 0xa5, 0x06, 0x83, 0x30, 0x21, 0x01],
  LowTemperatureSubmit: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x00, 0x01, 0x00, 0x88],
  MidTemperatureConfirm: [0x5a, 0xa5, 0x06, 0x83, 0x30, 0x22, 0x01],
  MidTemperatureSubmit: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x00, 0x01, 0x00, 0x89],
  HighTemperatureConfirm: [0x5a, 0xa5, 0x06, 0x83, 0x30, 0x23, 0x01],
  HighTemperatureSubmit: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x00, 0x01, 0x00, 0x8a],
  SaveExit: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x00, 0x01, 0x00, 0x8b],
  NotSaveExit: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x00, 0x01, 0x00, 0x8c]
}

export const TemperatureCalibrationPageFourSend = {
  LowTemperatureConfirm: [0x5a, 0xa5, 0x06, 0x83, 0x30, 0x31, 0x01],
  LowTemperatureSubmit: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x00, 0x01, 0x00, 0xb0],
  MidTemperatureConfirm: [0x5a, 0xa5, 0x06, 0x83, 0x30, 0x32, 0x01],
  MidTemperatureSubmit: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x00, 0x01, 0x00, 0xb1],
  HighTemperatureConfirm: [0x5a, 0xa5, 0x06, 0x83, 0x30, 0x33, 0x01],
  HighTemperatureSubmit: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x00, 0x01, 0x00, 0xb2],
  SaveExit: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x00, 0x01, 0x00, 0xb3],
  NotSaveExit: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x00, 0x01, 0x00, 0xb4]
}

export type TemperatureCalibrationChooseMethods =
  | 'TemperatureCalibrationChooseSend.One'
  | 'TemperatureCalibrationChooseSend.Two'
  | 'TemperatureCalibrationChooseSend.Three'
  | 'TemperatureCalibrationChooseSend.Four'
  | 'TemperatureCalibrationChooseSend.Exit'

export type TemperatureCalibrationPageOneMethods =
  | 'TemperatureCalibrationPageOneSend.Init'
  | 'TemperatureCalibrationPageOneSend.LowTemperatureConfirm'
  | 'TemperatureCalibrationPageOneSend.LowTemperatureSubmit'
  | 'TemperatureCalibrationPageOneSend.MidTemperatureConfirm'
  | 'TemperatureCalibrationPageOneSend.MidTemperatureSubmit'
  | 'TemperatureCalibrationPageOneSend.HighTemperatureConfirm'
  | 'TemperatureCalibrationPageOneSend.HighTemperatureSubmit'
  | 'TemperatureCalibrationPageOneSend.SaveExit'
  | 'TemperatureCalibrationPageOneSend.NotSaveExit'

export type TemperatureCalibrationPageTwoMethods =
  | 'TemperatureCalibrationPageTwoSend.Init'
  | 'TemperatureCalibrationPageTwoSend.LowTemperatureConfirm'
  | 'TemperatureCalibrationPageTwoSend.LowTemperatureSubmit'
  | 'TemperatureCalibrationPageTwoSend.MidTemperatureConfirm'
  | 'TemperatureCalibrationPageTwoSend.MidTemperatureSubmit'
  | 'TemperatureCalibrationPageTwoSend.HighTemperatureConfirm'
  | 'TemperatureCalibrationPageTwoSend.HighTemperatureSubmit'
  | 'TemperatureCalibrationPageTwoSend.SaveExit'
  | 'TemperatureCalibrationPageTwoSend.NotSaveExit'

export type TemperatureCalibrationPageThreeMethods =
  | 'TemperatureCalibrationPageThreeSend.Init'
  | 'TemperatureCalibrationPageThreeSend.LowTemperatureConfirm'
  | 'TemperatureCalibrationPageThreeSend.LowTemperatureSubmit'
  | 'TemperatureCalibrationPageThreeSend.MidTemperatureConfirm'
  | 'TemperatureCalibrationPageThreeSend.MidTemperatureSubmit'
  | 'TemperatureCalibrationPageThreeSend.HighTemperatureConfirm'
  | 'TemperatureCalibrationPageThreeSend.HighTemperatureSubmit'
  | 'TemperatureCalibrationPageThreeSend.SaveExit'
  | 'TemperatureCalibrationPageThreeSend.NotSaveExit'

export type TemperatureCalibrationPageFourMethods =
  | 'TemperatureCalibrationPageFourSend.Init'
  | 'TemperatureCalibrationPageFourSend.LowTemperatureConfirm'
  | 'TemperatureCalibrationPageFourSend.LowTemperatureSubmit'
  | 'TemperatureCalibrationPageFourSend.MidTemperatureConfirm'
  | 'TemperatureCalibrationPageFourSend.MidTemperatureSubmit'
  | 'TemperatureCalibrationPageFourSend.HighTemperatureConfirm'
  | 'TemperatureCalibrationPageFourSend.HighTemperatureSubmit'
  | 'TemperatureCalibrationPageFourSend.SaveExit'
  | 'TemperatureCalibrationPageFourSend.NotSaveExit'
