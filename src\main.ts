import { createApp } from 'vue'
import './styles/index.css'
import App from './App.vue'
// import './samples/node-api'

import { router } from '@/router'
import Notifications from '@kyvg/vue3-notification'

import { setupStore } from '@/store'

const app = createApp(App)

setupStore(app)
app
  .use(router)
  .use(Notifications)
  .mount('#app')
  .$nextTick(() => {
    postMessage({ payload: 'removeLoading' }, '*')
    localStorage.setItem('loginTime', new Date().toString())
  })
