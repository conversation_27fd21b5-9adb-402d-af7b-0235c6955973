import { defineStore } from 'pinia'
import { store } from '@/store'

import type { ISerialportState } from '../types'

export const useSerialportStore = defineStore({
  id: 'serialport-store',
  state: (): ISerialportState => ({
    ready: false,
    switched: true
  }),
  getters: {
    getReady(state): boolean {
      return state.ready
    },
    getSwitched(state): boolean {
      return state.switched
    }
  },
  actions: {
    setReady(data: boolean) {
      this.ready = data
    },
    setSwitched(data: boolean) {
      this.switched = data
    }
  }
})

export function useSerialportStoreHook() {
  return useSerialportStore(store)
}
