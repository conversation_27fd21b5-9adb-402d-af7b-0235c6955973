<script setup lang="ts">
import { ref, unref, onMounted, watch, PropType, computed, nextTick } from 'vue'
import echarts from '@/utils/echarts'
import { isFourChannel } from '@/utils'

type RealtimeChartData = {
  power: number[][]
  temperatureOne: number[][]
  temperatureTwo: number[][]
  temperatureThree: number[][]
  temperatureFour: number[][]
}

const props = defineProps({
  data: {
    type: Object as () => RealtimeChartData,
    required: true
  },
  time: {
    type: String,
    default: ''
  },
  hideZoom: {
    type: Boolean,
    default: false
  }
})

// chart
const chartRef = ref<HTMLDivElement | null>(null)
let chartInstance: echarts.ECharts | null = null
const cureTime = computed(() => parseInt(props.time))

function formatTime(time: number) {
  if (time) {
    return `${Math.floor(time / 60)}:${time % 60}`
  }
  return ''
}

onMounted(() => {
  const el = unref(chartRef)
  if (!el || !unref(el)) {
    return
  }

  chartInstance = echarts.init(el)

  const option: echarts.EChartsCoreOption = {
    animation: false,
    dataZoom: props.hideZoom ? undefined : [
      {
        type: 'slider',
        show: true,
        realtime: true,
        start: 0,
        end: 100,
        xAxisIndex: [0, 1],
        labelFormatter: (value: number) => {
          return Math.round(value / 60)
        },
        bottom: 10,
        height: 20,
        textStyle: {
          color: '#333'
        }
      },
      {
        type: 'inside',
        realtime: true,
        start: 0,
        end: 100,
        xAxisIndex: [0, 1]
      }
    ],
    // legend: { data: ['功率', '温度T1', '温度T2', '温度T3', '温度T4'] },
    color: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#9a60b4', '#fc8452', '#73c0de', '#3ba272', '#ea7ccc'],
    tooltip: {
      show: true,
      trigger: 'axis',
      formatter(params: any) {
        // console.log('tooltip params : ', params)
        let power: any = {}
        let temperatureOne: any = {}
        let temperatureTwo: any = {}
        let temperatureThree: any = {}
        let temperatureFour: any = {}
        if (params && params.length > 5) {
          power = params[0]
          temperatureOne = params[2]
          temperatureTwo = params[4]
          temperatureThree = params[6]
          temperatureFour = params[8]
        } else {
          power = params[0]
          temperatureOne = params[1]
          temperatureTwo = params[2]
          temperatureThree = params[3]
          temperatureFour = params[4]
        }
        return `
          <div>
            <div>时间：${formatTime(power.data[0])}</div>
            <div>${power.marker} <span>功率：${power.data[1] || ''}</span></div>
            <div>${temperatureOne.marker} <span>温度T1：${temperatureOne.data[1] || ''}</span></div>
            <div>${temperatureTwo.marker} <span>温度T2：${temperatureTwo.data[1] || ''}</span></div>
            ${isFourChannel
            ?
            `
              <div>${temperatureThree.marker} <span>温度T3：${temperatureThree.data[1] || ''}</span></div>
              <div>${temperatureFour.marker} <span>温度T4：${temperatureFour.data[1] || ''}</span></div>
            `
            :
            ''}
          </div>
        `
      }
    },
    grid: {
      top: 40,
      left: 50,
      right: 73,
      bottom: 50
    },
    xAxis: [{
      name: '时间(min)',
      nameGap: 20,
      min: 0,
      max: cureTime.value * 60,
      type: 'value',
      splitNumber: cureTime.value * 2,
      axisLine: {
        lineStyle: {
          color: '#333',
        },
        symbol: ['none', 'none'],
        symbolSize: [7, 15],
        symbolOffset: 15,
        show: true,
      },
      axisLabel: {
        showMaxLabel: true,
        formatter: (value: number) => {
          // console.log('axis label formatter : ', cureTime.value, value, value % 120 === 0 ? Math.round(value / 120) : '', value % (cureTime.value * 60))
          if (value % (cureTime.value * 60) === 0) {
            // console.log('show this label : ', Math.round(value / 60))
            return Math.round(value / 60)
            // return 1
          } else if (cureTime.value < 16) {
            return value % 60 === 0 ? Math.round(value / 60) : ''
          } else if (cureTime.value < 31) {
            if (cureTime.value % 2 === 0) {
              return value % 120 === 0 ? Math.round(value / 60) : ''
            } else {
              return value % 120 === 60 ? Math.round(value / 60) : ''
            }
          } else if (cureTime.value < 61) {
            if (cureTime.value % 4 === 0) {
              return value % 240 === 0 ? Math.round(value / 60) : ''
            } else if (cureTime.value % 4 === 1) {
              return value % 240 === 60 ? Math.round(value / 60) : ''
            } else if (cureTime.value % 4 === 2) {
              return value % 240 === 120 ? Math.round(value / 60) : ''
            } else if (cureTime.value % 4 === 3) {
              return value % 240 === 180 ? Math.round(value / 60) : ''
            }
          } else if (cureTime.value < 121) {
            if (cureTime.value % 8 === 0) {
              return value % 480 === 0 ? Math.round(value / 60) : ''
            } else if (cureTime.value % 8 === 1) {
              return value % 480 === 60 ? Math.round(value / 60) : ''
            } else if (cureTime.value % 8 === 2) {
              return value % 480 === 120 ? Math.round(value / 60) : ''
            } else if (cureTime.value % 8 === 3) {
              return value % 480 === 180 ? Math.round(value / 60) : ''
            } else if (cureTime.value % 8 === 4) {
              return value % 480 === 240 ? Math.round(value / 60) : ''
            } else if (cureTime.value % 8 === 5) {
              return value % 480 === 300 ? Math.round(value / 60) : ''
            } else if (cureTime.value % 8 === 6) {
              return value % 480 === 360 ? Math.round(value / 60) : ''
            } else if (cureTime.value % 8 === 7) {
              return value % 480 === 420 ? Math.round(value / 60) : ''
            }
          }
        }
      },
      splitLine: {
        show: false
      }
    }],
    yAxis: [
      {
        name: '功率(W)',
        nameGap: 20,
        min: 0,
        max: 20,
        type: 'value',
        axisLine: {
          lineStyle: {
            color: '#333'
          },
          symbol: ['none', 'arrow'],
          symbolSize: [7, 15],
          symbolOffset: 15,
          show: true
        },
        splitLine: {
          show: false
        }
      },
      {
        name: '温度(℃)',
        nameGap: 20,
        min: 0,
        max: 55,
        type: 'value',
        axisLine: {
          lineStyle: {
            color: '#333'
          },
          symbol: ['none', 'arrow'],
          symbolSize: [7, 15],
          symbolOffset: 15,
          show: true
        },
        splitLine: {
          show: false
        },
      }
    ],
    series: [
      {
        name: '功率',
        type: 'line',
        showSymbol: false,
        areaStyle: {
          opacity: 0.8,
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: 'rgba(155, 157, 160, 0.4)'
            },
            {
              offset: 1,
              color: 'rgba(155, 157, 160, 0.4)'
            }
          ])
        },
        clip: true,
        data: [],
      },
      {
        name: "温度T1",
        type: 'line',
        showSymbol: false,
        clip: true,
        data: [],
        yAxisIndex: 1
      },
      {
        name: "温度T2",
        type: 'line',
        showSymbol: false,
        clip: true,
        data: [],
        yAxisIndex: 1
      },
      {
        name: "温度T3",
        type: 'line',
        showSymbol: false,
        clip: true,
        data: [],
        yAxisIndex: 1
      },
      {
        name: "温度T4",
        type: 'line',
        showSymbol: false,
        clip: true,
        data: [],
        yAxisIndex: 1
      }
    ]
  }

  chartInstance.setOption(option)
})

watch(() => props.data,
  ({ power, temperatureOne, temperatureTwo, temperatureThree, temperatureFour }) => {
    console.log('realtime chart data : ', power, temperatureOne, temperatureTwo, temperatureThree, temperatureFour)
    // console.log('raw power data : ', power ?? [])
    const maxPower = power.length ? Math.max(...power.map(item => item[1])) : 20
    const yAxisMax = (Math.floor(maxPower / 20) + (maxPower % 20 === 0 ? 0 : 1)) * 20
    // let formatedData: RealtimeChartData = {
    //   power: [], temperatureOne: [], temperatureTwo: [], temperatureThree: [], temperatureFour: []
    // }
    // const endTime = cureTime.value * 60
    // if (power?.length) {
    //   const lastPowerData = power[power.length - 1]
    //   const time = lastPowerData[0]
    //   const data = lastPowerData[1]
    //   const compensationArr: number[][] = []
    //   if (time < endTime && endTime - time <= 3) {
    //     const diff = cureTime.value * 60 - time
    //     for (let index = 0; index < diff; index++) {
    //       compensationArr.push([time + (index + 1), data])
    //     }

    //     formatedData.power = power.concat(compensationArr)
    //   } else {
    //     formatedData.power = power
    //   }
    // }

    // if (temperatureOne?.length) {
    //   const lastTemperatureOneData = temperatureOne[temperatureOne.length - 1]
    //   const time = lastTemperatureOneData[0]
    //   const data = lastTemperatureOneData[1]
    //   const compensationArr: number[][] = []
    //   if (time < endTime && endTime - time <= 3) {
    //     const diff = cureTime.value * 60 - time
    //     for (let index = 0; index < diff; index++) {
    //       compensationArr.push([time + (index + 1), data])
    //     }

    //     formatedData.temperatureOne = temperatureOne.concat(compensationArr)
    //   } else {
    //     formatedData.temperatureOne = temperatureOne
    //   }
    // }

    // if (temperatureTwo?.length) {
    //   const lastTemperatureTwoData = temperatureTwo[temperatureTwo.length - 1]
    //   const time = lastTemperatureTwoData[0]
    //   const data = lastTemperatureTwoData[1]
    //   const compensationArr: number[][] = []
    //   if (time < endTime && endTime - time <= 3) {
    //     const diff = cureTime.value * 60 - time
    //     for (let index = 0; index < diff; index++) {
    //       compensationArr.push([time + (index + 1), data])
    //     }

    //     formatedData.temperatureTwo = temperatureTwo.concat(compensationArr)
    //   } else {
    //     formatedData.temperatureTwo = temperatureTwo
    //   }
    // }

    // if (temperatureThree?.length) {
    //   const lastTemperatureThreeData = temperatureThree[temperatureThree.length - 1]
    //   const time = lastTemperatureThreeData[0]
    //   const data = lastTemperatureThreeData[1]
    //   const compensationArr: number[][] = []
    //   if (time < endTime && endTime - time <= 3) {
    //     const diff = cureTime.value * 60 - time
    //     for (let index = 0; index < diff; index++) {
    //       compensationArr.push([time + (index + 1), data])
    //     }

    //     formatedData.temperatureThree = temperatureThree.concat(compensationArr)
    //   } else {
    //     formatedData.temperatureThree = temperatureThree
    //   }
    // }

    // if (temperatureFour?.length) {
    //   const lastTemperatureFourData = temperatureFour[temperatureFour.length - 1]
    //   const time = lastTemperatureFourData[0]
    //   const data = lastTemperatureFourData[1]
    //   const compensationArr: number[][] = []
    //   if (time < endTime && endTime - time <= 3) {
    //     const diff = cureTime.value * 60 - time
    //     for (let index = 0; index < diff; index++) {
    //       compensationArr.push([time + (index + 1), data])
    //     }

    //     formatedData.temperatureFour = temperatureFour.concat(compensationArr)
    //   } else {
    //     formatedData.temperatureFour = temperatureFour
    //   }
    // }

    nextTick(() => {
      chartInstance?.setOption({
        yAxis: [{
          name: "功率(W)",
          max: Math.max(yAxisMax, 20),
        }],
        series: [
          { name: "功率", data: power ?? [] },
          { name: "温度T1", data: temperatureOne ?? [] },
          { name: "温度T2", data: temperatureTwo ?? [] },
          { name: "温度T3", data: temperatureThree ?? [] },
          { name: "温度T4", data: temperatureFour ?? [] },
        ]
      })
    })
  },
  { deep: true, immediate: true }
)

const maxPower = computed(() => {
  const maxPower = props.data.power.length ? Math.max(...props.data.power.map(item => item[1])) : 20
  const yAxisMax = (Math.floor(maxPower / 20) + (maxPower % 20 === 0 ? 0 : 1)) * 20
  return Math.max(yAxisMax, 20)
})

watch(cureTime, time => {
  // console.log('props time change : ', time, time * 60)
  chartInstance?.setOption({
    xAxis: {
      max: time * 60,
      splitNumber: time * 2,
    }
  })
}, {
  immediate: true
})

watch(() => maxPower, max => {
  chartInstance?.setOption({
    yAxis: [
      {
        name: "功率(W)",
        max,
      }
    ]
  })
})

function getChartOption() {
  return chartInstance?.getOption()
}

function resetYLabel() {
  chartInstance?.setOption({
    yAxis: {
      max: 20,
    }
  })
}

function getDataURL() {
  return chartInstance?.getDataURL() || ""
}

defineExpose({
  getChartOption,
  resetYLabel,
  getDataURL
})
</script>

<template>
  <div ref="chartRef"></div>
</template>
