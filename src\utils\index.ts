import dayjs from 'dayjs'

export function getPageNoBuffer(pageNo: number): number[] {
  return [0, pageNo]
}

export enum PageNoEnum {
  Loading = 0,
  Home = 63,
  ActionChoose = 3,
  CureChoose = 35,
  CurePulse = 37,
  <PERSON><PERSON><PERSON>gle = 39,
  <PERSON><PERSON><PERSON> = 41,
  CureContinuation = 5,
  CureContinuationRealtimePower = 64,
  CalibrationChoose = 45,
  CalibrationTemperatureOne = 7,
  CalibrationTemperatureTwo = 47,
  CalibrationPower = 9,
  IntroductionChoose = 23,
  IntroductionCompany = 25,
  IntroductionPrinciple = 27,
  IntroductionScope = 29,
  IntroductionAttention = 31,
  IntroductionTroubleshooting = 33,
  IntroductionContraindication = 57
}

export function preciseTimer(callback: any, interval: number) {
  let startTime: number | null = null
  let frameId: number | null = null

  function loop(timestamp: number) {
    if (!startTime) {
      startTime = timestamp
    }

    const elapsedTime = timestamp - startTime

    if (elapsedTime >= interval) {
      callback()
      startTime = timestamp
    }

    frameId = requestAnimationFrame(loop)
  }

  frameId = requestAnimationFrame(loop)

  function cancelTimer() {
    if (frameId) cancelAnimationFrame(frameId)
  }

  return cancelTimer
}

export function formatTemperature(temperature: string) {
  if (temperature.length === 4) {
    return temperature.slice(0, 2) + '.' + temperature.slice(2, 4)
  } else if (temperature.length === 3) {
    return temperature.slice(0, 1) + '.' + temperature.slice(1, 3)
  } else if (temperature.length === 2) {
    return `0.${temperature}`
  } else if (temperature.length === 1) {
    return `0.0${temperature}`
  } else {
    return ''
  }
}

export function formatDate(
  date: Date | string | undefined | null,
  template: string = 'YYYY-MM-DD HH:mm:ss'
) {
  return date ? dayjs(date).format(template) : ''
}

export enum ChannelCountEnum {
  FOUR = 'four',
  TWO = 'two'
}

export const isFourChannel =
  import.meta.env.VITE_CHANNEL_COUNT === ChannelCountEnum.FOUR
