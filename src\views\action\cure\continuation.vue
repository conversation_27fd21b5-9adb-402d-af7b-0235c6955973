<script setup lang="ts">
import PageWrapper from '@/components/PageWrapper/index.vue'
import { ref } from 'vue';
import CurePanel from './components/CurePanel.vue'
import WaveChart from './components/WaveChart.vue'
import RealtimePowerChart from './components/RealtimePowerChart.vue'
import { useWavePage } from './common/useWavePage'

defineOptions({
  name: 'CureContinuationPage'
})

// 启动后绘制波形
function draw() {
  waveChartData.value = generateChartData()

  if (drawCount.value < 1000) {
    drawCount.value += 5
  } else {
    drawCount.value = 0
  }
}

function generateChartData(): [number, number][] {
  let data: [number, number][] = []
  const len = Math.min(drawCount.value, 1000)
  console.log('offset len : ', len)
  for (let i = 0; i < len; i++) {
    const x = i;
    const y = 4;
    data.push([x, y]);
  }

  return data
}

const {
  loading,
  drawCount,
  waveChartData,
  realtimeChartData,
  parameters,
  state,
  panelRef,
  realtimeChartRef,
  handleLogout,
  goMedical,
  touchScreenInfo,
  touchScreenVersionNameArr,
} = useWavePage({
  generateChartData,
  drawFunc: draw,
  drawInterval: 50,
  type: 'continuation'
})

const formType = ref('continuation')
function onTopRightClick() {
  window.electronAPI.serialport.dispatch('WavePage.ToggleRealtimePower')
  if (state.lockscreen) return
  if (formType.value === 'continuation') {
    formType.value = 'continuation-realtime-power'
  } else {
    formType.value = 'continuation'
  }
}
</script>

<template>
  <PageWrapper :show-logout="false" :logoutAutoBack="false" @logout="handleLogout">
    <div class="absolute w-20 h-20 right-0 top-0" @click="onTopRightClick"></div>
    <div class="content-wrapper pt-4 pb-6 px-4 w-[1450px] h-[790px] text-white flex">
      <div v-if="loading" v-loading="loading" element-loading-background="rgba(122, 122, 122, 0.2)"
        element-loading-text="加载中..." class="absolute top-0 left-0 right-0 bottom-0 z-10 loading-div"></div>
      <CurePanel ref="panelRef" v-model:boot="state.boot" v-model:pause="state.pause" :mute="state.mute"
        :lockscreen="state.lockscreen" v-bind="parameters" title="连续波输出指示" :type="formType" class="w-80 h-full mr-8"
        @go-medical-record="goMedical" @logout="handleLogout" />
      <div class="flex-1 p-4 col-flex mb-10">
        <WaveChart :data="waveChartData" class="w-full h-1/2" :yAxisMax="5" :xAxisMax="1000" />
        <RealtimePowerChart ref="realtimeChartRef" :data="realtimeChartData" :time="parameters.time"
          class="w-full h-1/2 mt-3" />
      </div>
    </div>
    <div ref="wavePageBottomRef" class="absolute bottom-5 w-56 flex-center wave-bottom-info">
      <div class="text-base font-bold w-56">
        <p class="w-full flex">
          <span class="inline-flex w-54 justify-between">
            <template v-if="touchScreenVersionNameArr.length">
              <template v-for="(item) in touchScreenVersionNameArr">
                <span>{{ item }}</span>
              </template>
              <span>：</span>
            </template>
          </span>
          <span class="inline-flex flex-1">{{ touchScreenInfo.version }}</span>
        </p>
      </div>
    </div>
  </PageWrapper>
</template>
<style scoped>
:global(.el-loading-spinner .circular) {
  --el-loading-spinner-size: 72px;
}

:global(.el-loading-spinner .el-loading-text) {
  font-size: 24px;
  margin-top: 10px;
}

.wave-bottom-info {
  left: calc((100% - 1390px) / 2);
}
</style>
