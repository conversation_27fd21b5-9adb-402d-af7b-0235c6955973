export const PowerCalibrationPageOneSend = {
  One: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x00, 0x01, 0x00, 0x40],
  Five: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x00, 0x01, 0x00, 0x41],
  Ten: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x00, 0x01, 0x00, 0x42],
  Twenty: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x00, 0x01, 0x00, 0x43],
  Forty: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x00, 0x01, 0x00, 0x44],
  Sixty: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x00, 0x01, 0x00, 0x45],
  Hundred: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x00, 0x01, 0x00, 0x46],
  OneHundredAndFifty: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x00, 0x01, 0x00, 0x47],
  TwoHundred: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x00, 0x01, 0x00, 0x48],
  ToggleStepValue: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x00, 0x01, 0x00, 0x4d],
  Up: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x00, 0x01, 0x00, 0x49],
  Down: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x00, 0x01, 0x00, 0x4a],
  Pause: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x00, 0x01, 0x00, 0x4f],
  Confirm: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x00, 0x01, 0x00, 0x4b],
  Exit: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x00, 0x01, 0x00, 0x4c]
}

export type PowerCalibrationPageOneSendMethods =
  | 'PowerCalibrationPageOneSend.One'
  | 'PowerCalibrationPageOneSend.Five'
  | 'PowerCalibrationPageOneSend.Ten'
  | 'PowerCalibrationPageOneSend.Twenty'
  | 'PowerCalibrationPageOneSend.Forty'
  | 'PowerCalibrationPageOneSend.Sixty'
  | 'PowerCalibrationPageOneSend.Hundred'
  | 'PowerCalibrationPageOneSend.OneHundredAndFifty'
  | 'PowerCalibrationPageOneSend.TwoHundred'
  | 'PowerCalibrationPageOneSend.ToggleStepValue'
  | 'PowerCalibrationPageOneSend.Up'
  | 'PowerCalibrationPageOneSend.Down'
  | 'PowerCalibrationPageOneSend.Pause'
  | 'PowerCalibrationPageOneSend.Confirm'
  | 'PowerCalibrationPageOneSend.Exit'
