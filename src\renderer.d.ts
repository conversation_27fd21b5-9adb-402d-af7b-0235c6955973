import Basic from '../electron/db/entities/basic.entity'
import Patient from '../electron/db/entities/patient.entity'
import PatientCase from '../electron/db/entities/patient-case.entity'
import User from '../electron/db/entities/user.entity'
import {
  UserQueryCriteria,
  PatientQueryCriteria,
  UserLoginParams,
  UserLoginResult,
  ResetUserPasswordParams,
  UpdateUserPasswordParams,
  BaseResult
} from '../electron/db/types'
import type { InsertResult, UpdateResult } from 'typeorm'
import type { IpcRendererEvent } from 'electron'

export interface ISerialportAPI {
  dispatch: (callName: string, ...args: ReadonlyArray<unknown>) => Promise<void>
  receive: (
    channel: string,
    listener: (event: IpcRendererEvent, ...args: any[]) => void
  ) => void
  removeListener: (channel: string) => void
  copyToClipboard: (text: string) => void
  readFromClipboard: () => string
  quitApp: () => void
}

export interface ISqliteAPI {
  getBasicInfo: () => Promise<Basic>
  initBasicInfo: () => Promise<Basic | InsertResult>
  updateBasicInfo: (basic: Basic) => Promise<Basic>
  checkCasePath: () => Promise<void>
  queryPatients: (params: PatientQueryCriteria) => Promise<[Patient[], number]>
  createPatient: (patient: Patient) => Promise<BaseResult & { data?: Patient }>
  updatePatient: (patient: Patient) => Promise<BaseResult & { data?: Patient }>
  deletePatient: (patientId: number) => Promise<UpdateResult>
  exportPdf: (...args: any[]) => void
  removePdf: (...args: any[]) => void
  // syncStoragePatients: () => Promise<void>
  // 病历信息
  queryPatientCases: (patientId: number) => Promise<PatientCase[]>
  createPatientCase: (
    patientId: number,
    patientCase: PatientCase
  ) => Promise<BaseResult>
  updatePatientCase: (
    patientId: number,
    patientCase: PatientCase
  ) => Promise<BaseResult>
  deletePatientCase: (patientCaseId: number) => Promise<UpdateResult>

  // 用户接口
  queryUsers: (queryCriteria: UserQueryCriteria) => Promise<User[]>
  createUser: (user: User) => Promise<BaseResult>
  updateUser: (user: User) => Promise<BaseResult>
  deleteUser: (userId: number) => Promise<UpdateResult>
  userLogin: (params: UserLoginParams) => Promise<UserLoginResult>
  updateUserPassword: (params: UpdateUserPasswordParams) => Promise<BaseResult>
  resetUserPassword: (params: ResetUserPasswordParams) => Promise<BaseResult>

  // excel
  exportExcel: (...args: any[]) => void
  removeExcel: (...args: any[]) => void
}

export interface IElectronAPI {
  serialport: ISerialportAPI
  sqlite: ISqliteAPI
}

declare global {
  interface Window {
    electronAPI: IElectronAPI
  }
}
