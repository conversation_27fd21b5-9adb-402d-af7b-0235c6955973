<template>
  <el-container class="h-screen">
    <el-header class="bg-primary text-white">
      <div class="flex justify-between items-center h-full relative">
        <h1 class="text-2xl font-bold">病历管理系统</h1>
        <el-button :icon="ArrowLeft" type="warning" plain @click="goBack">返回主界面</el-button>

        <div class="absolute left-48 flex items-center text-2xl">
          <span v-if="switched" class="rounded-2xl w-6 h-6 bg-red-500 mr-3"></span>
          <span v-else class="rounded-2xl w-6 h-6 bg-green-500 mr-3"></span>

          <span v-if="switched">未连接</span>
          <span v-else>已连接</span>
        </div>
      </div>
    </el-header>
    <el-container>
      <el-aside width="300px" class="border-r border-gray-200">
        <div class="p-4">
          <el-input v-model="searchQuery" placeholder="搜索患者姓名/编号" class="mb-4" clearable>
            <template #prefix>
              <el-icon>
                <Search class="cursor-pointer" @click="handleSearch()" />
              </el-icon>
            </template>
          </el-input>
          <el-scrollbar height="calc(100vh - 190px)">
            <el-menu :default-active="String(selectedPatient?.id)" @select="handleSelect">
              <el-menu-item v-for="patient in patients" :key="patient.id" :index="String(patient.id)">
                <template #title>
                  <span>{{ patient.name }}</span>
                  <span class="text-gray-500 text-sm ml-2">（{{ patient.sn }}）</span>
                </template>
              </el-menu-item>
            </el-menu>
          </el-scrollbar>
          <el-button type="primary" class="mt-4 w-full" :icon="Plus" @click="handleCreatePatient">
            新增患者信息
          </el-button>
        </div>
      </el-aside>
      <el-main>
        <div v-if="selectedPatient">
          <div class="flex justify-between items-center mb-6">
            <h2 class="font-bold">基本信息 - {{ selectedPatient.name }}</h2>
            <div>
              <el-button v-if="!router.currentRoute.value.query.patientId" type="primary" @click="goActionPage">
                <el-icon class="mr-2">
                  <Operation />
                </el-icon>下一步
              </el-button>
              <el-button type="primary" @click="handleEditPatient">
                <el-icon class="mr-2">
                  <Edit />
                </el-icon>编辑
              </el-button>
              <el-button type="danger" @click="handleDeletePatient">
                <el-icon class="mr-2">
                  <Delete />
                </el-icon>删除
              </el-button>
            </div>
          </div>
          <el-descriptions :column="3" border>
            <el-descriptions-item label="病历编号">
              <span class="flex items-center">
                <el-icon size="16px" class="mr-2">
                  <Document />
                </el-icon>
                <span>{{ selectedPatient.sn }}</span>
              </span>
            </el-descriptions-item>
            <el-descriptions-item label="性别">
              <span class="flex items-center">
                <el-icon size="16px" class="mr-2">
                  <Male v-if="selectedPatient.sex === 0" />
                  <Female v-else />
                </el-icon>
                <span>{{ selectedPatient.sex === 0 ? '男' : '女' }}
                </span>
              </span>
            </el-descriptions-item>
            <el-descriptions-item label="年龄">
              <span class="flex items-center">
                <el-icon size="16px" class="mr-2">
                  <User />
                </el-icon>
                <span>{{ selectedPatient.age }}</span>
              </span>
            </el-descriptions-item>
            <el-descriptions-item label="体重">
              <span class="flex items-center">
                <el-icon v-if="selectedPatient.weight" size="16px" class="mr-2">
                  <Star />
                </el-icon>
                <span v-if="selectedPatient.weight">{{ selectedPatient.weight }} kg</span>
              </span>
            </el-descriptions-item>
            <el-descriptions-item label="治疗医生">
              <span class="flex items-center">
                <el-icon v-if="selectedPatient.doctorName" size="16px" class="mr-2">
                  <Avatar />
                </el-icon>
                <span>{{ selectedPatient.doctorName }}</span>
              </span>
            </el-descriptions-item>
          </el-descriptions>
          <div class="mt-6">
            <PatientCaseTable ref="patientCaseTableRef" :patient="selectedPatient" />
          </div>
        </div>
        <el-empty v-else description="请选择一个病历查看详情" />
      </el-main>
    </el-container>
    <CreatePatient ref="createPatientRef" @close="onCreatePatientModalDone" />
  </el-container>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, unref, watch, nextTick } from 'vue'
import { useRouter } from 'vue-router';
// import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft, Search, Plus, Edit, Delete, User, Male, Female, Document, Avatar, Star, Operation } from '@element-plus/icons-vue'
import CreatePatient from './CreatePatient.vue';
import PatientCaseTable from './PatientCaseTable.vue';
import { UserRole, type Patient } from '@/types/entity'
import { useUserStore } from '@/store/modules/user';
import { throttle } from 'lodash-es'
import { useSerialportStore } from '@/store/modules/serialport';
import { useMedicalStore } from '@/store/modules/medical';

defineOptions({
  name: 'PatientCaseManagement'
})

const router = useRouter()
const userStore = useUserStore()
const serialportStore = useSerialportStore()
const medicalStore = useMedicalStore()

// 返回上一级页面
function goBack() {
  ElMessageBox.confirm('有用的曲线应先到病历管理程序中存盘！您确定要继续退出吗？', '特别注意', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    router.replace('/home')
  }).catch(() => { })
}

const searchQuery = ref('')
const selectedPatientId = ref<number | null>(null)
const createPatientRef = ref()
const patientCaseTableRef = ref()

const patients = ref<Patient[]>([])

async function handleSearch(patientId?: string | number) {
  const [list] = await window.electronAPI.sqlite.queryPatients({ keyword: searchQuery.value, userId: userStore.getUserId!, role: userStore.getUserRole! })

  patients.value = list
  console.log('patient list : ', list, patientId)

  if (patientId) {
    console.log('1111111111111111111')
    handleSelect(parseInt(patientId as string))
  } else if (patients.value.length) {
    console.log('222222222222')
    handleSelect(patients.value[0].id)

  }
}

// 使用 throttle 包装处理函数
const handleInputChange = throttle(function () {
  handleSearch()
}, 300);

// 监听 inputValue 的变化
watch(searchQuery, () => {
  handleInputChange();
});

onMounted(async () => {
  const { patientId } = router.currentRoute.value.query
  console.log('query patient id : ', patientId, router.currentRoute.value.query, router.currentRoute.value)
  if (patientId) {
    await handleSearch(patientId as unknown as string)
  } else {
    await handleSearch()
  }
  console.log(`patients.value : `, patients.value.length, patients.value)
  medicalStore.setCurrentPatientId(null)
  if (patients.value.length === 0) {
    handleCreatePatient()
  }
})

const selectedPatient = computed(() => patients.value.find(c => c.id === selectedPatientId.value))

const handleSelect = (index: string | number) => {
  console.log('handleSelect', index)
  // if (isNaN())
  selectedPatientId.value = Number(index)
}

const handleCreatePatient = () => {
  createPatientRef.value?.open()
}

const handleEditPatient = () => {
  createPatientRef.value?.open(unref(selectedPatient))
}

const handleDeletePatient = () => {
  ElMessageBox.confirm(
    '确定要删除这个患者吗？此操作不可逆。',
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(() => {
      const patientName = selectedPatient.value?.name
      const patientSn = selectedPatient.value?.sn
      window.electronAPI.sqlite.deletePatient(selectedPatientId.value!).then(async res => {
        console.log('delete result : ', res)
        if (res) {
          ElMessage({
            showClose: true,
            message: '删除成功！',
            type: 'success',
            grouping: true
          })
          handleSearch()
          window.electronAPI.sqlite.removePdf({
            casePath: `${userStore.getCasePath}\\${userStore.getLoginName}`,
            nameWithNo: `${patientName}(${patientSn})`
          })
          window.electronAPI.sqlite.removeExcel({
            casePath: `${userStore.getCasePath}\\${userStore.getLoginName}`,
            nameWithNo: `${patientName}(${patientSn})`
          })
        }
      })
      selectedPatientId.value = null
      ElMessage.success('删除成功')
    })
    .catch(() => {
      // ElMessage.info('已取消删除')
    })
}

const switched = computed(() => {
  return serialportStore.getSwitched
})
function goActionPage() {
  if (switched.value) {
    ElMessage({
      showClose: false,
      message: '连接断开',
      type: 'warning',
      grouping: true
    })
    return
  }
  medicalStore.setCurrentPatientId(selectedPatientId.value)
  medicalStore.setCureParams(null)
  window.electronAPI.serialport.dispatch('StartingUp.Action')
  router.push({ path: '/action/choose' })
}

async function onCreatePatientModalDone({ patientId }: { patientId: number }) {
  await handleSearch(patientId)
  nextTick(() => {
    setTimeout(() => {
      patientCaseTableRef.value?.updateCaseFiles?.()
    }, 300)
  })
}
</script>

<style scoped>
.el-header {
  padding: 0 20px;
}

.el-menu.el-menu--horizontal {
  border-bottom: none;
}

.el-main {
  padding: 20px;
}
</style>
