{"name": "nova-nw8000", "version": "1.0.18", "main": "dist-electron/main/index.js", "description": "江苏诺万医疗设备有限公司 NW-8000微波治疗仪", "author": "江苏诺万医疗设备有限公司", "license": "MIT", "private": true, "keywords": ["electron", "rollup", "vite", "vue3", "vue"], "debug": {"env": {"VITE_DEV_SERVER_URL": "http://127.0.0.1:3344/"}}, "scripts": {"dev": "vite", "dev:two": "vite --mode two", "build": "vue-tsc --noEmit && vite build", "build:two": "vue-tsc --noEmit && vite build  --mode two", "package": "electron-builder", "preview": "vite preview", "postinstall": "electron-builder install-app-deps"}, "devDependencies": {"@originjs/vite-plugin-commonjs": "^1.0.3", "@tailwindcss/typography": "^0.5.9", "@types/dom-serial": "^1.0.3", "@types/pdfmake": "^0.2.2", "@vitejs/plugin-vue": "^4.0.0", "autoprefixer": "^10.4.13", "electron": "^22.0.0", "electron-builder": "^23.6.0", "postcss": "^8.4.21", "rollup-plugin-copy": "^3.4.0", "tailwindcss": "^3.2.7", "typescript": "^4.9.4", "unplugin-auto-import": "^0.16.1", "unplugin-vue-components": "^0.24.1", "vite": "^5.4.19", "vite-plugin-electron": "^0.11.1", "vite-plugin-electron-renderer": "^0.11.4", "vite-plugin-static-copy": "^3.0.0", "vite-plugin-utils": "^0.4.0", "vue": "^3.2.45", "vue-tsc": "^1.0.16"}, "dependencies": {"@cornerstonejs/core": "^3.22.3", "@cornerstonejs/tools": "^3.22.3", "@kyvg/vue3-notification": "^2.9.0", "dicom-parser": "^1.8.21", "@vueuse/core": "^9.13.0", "aws-sdk": "^2.1343.0", "better-sqlite3": "^8.2.0", "daisyui": "^2.50.1", "dayjs": "^1.11.7", "echarts": "^5.4.1", "electron-log": "^4.4.8", "element-plus": "^2.3.5", "exceljs": "^4.4.0", "iconv-lite": "^0.6.3", "lodash": "^4.17.21", "mock-aws-s3": "^4.0.2", "nock": "^13.3.0", "pdfjs-dist": "^2.16.105", "pdfmake": "^0.2.7", "pinia": "^2.1.3", "serialport": "^13.0.0", "typeorm": "^0.3.12", "vue-router": "^4.1.6"}}