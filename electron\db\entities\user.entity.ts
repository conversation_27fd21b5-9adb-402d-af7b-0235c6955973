import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn
} from 'typeorm'

export enum UserRole {
  SUPER = 'Super',
  ADMIN = 'Admin',
  USER = 'User'
}

@Entity({ name: 'user' }) // 实体
export default class User {
  @PrimaryGeneratedColumn() // 自增主键
  id: number

  @Column({ name: 'login_name', type: 'varchar' }) // Column 普通列
  loginName: string // js 数据类型

  @Column({ name: 'name', type: 'varchar', nullable: true }) // Column 普通列
  name: string // js 数据类型

  @Column({ name: 'password', type: 'varchar' }) // type 数据库键类型
  password: string

  @Column({
    name: 'role',
    type: 'varchar',
    enum: UserRole,
    default: UserRole.USER
  }) // type 数据库键类型
  role: UserRole

  @Column({
    type: 'datetime',
    name: 'latest_login_time',
    comment: '更新时间',
    nullable: true
  })
  latestLoginTime: Date

  @CreateDateColumn({
    type: 'datetime',
    name: 'created_time',
    comment: '创建时间'
  })
  createdTime: Date

  @UpdateDateColumn({
    type: 'datetime',
    name: 'updated_time',
    comment: '更新时间'
  })
  updateTime: Date

  @DeleteDateColumn({
    type: 'datetime',
    name: 'deleted_time',
    nullable: true,
    comment: '删除时间'
  })
  deletedTime: Date
}
