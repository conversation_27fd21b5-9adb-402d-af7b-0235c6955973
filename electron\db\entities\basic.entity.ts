import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn
} from 'typeorm'

@Entity({ name: 'basic' }) // 实体
export default class Basic {
  @PrimaryGeneratedColumn() // 自增主键
  id: number

  @Column({ name: 'company_name', type: 'varchar' }) // Column 普通列
  companyName: string // js 数据类型

  @Column({ name: 'sale_tel', type: 'varchar' }) // type 数据库键类型
  saleTel: string

  @Column({ name: 'after_sale_tel', type: 'varchar' }) // type 数据库键类型
  afterSaleTel: string

  @Column({ type: 'varchar' }) // type 数据库键类型
  website: string

  @Column({ name: 'touch_screen_version', type: 'varchar' }) // type 数据库键类型
  touchScreenVersion: string

  @Column({ name: 'touch_screen_version_name', type: 'varchar' }) // type 数据库键类型
  touchScreenVersionName: string

  @Column({ name: 'main_control_board_version', type: 'varchar' }) // type 数据库键类型
  mainControlBoardVersion: string

  @Column({ name: 'instrument_model', type: 'varchar' }) // nullable，非必须
  instrumentModel: string

  @Column({ name: 'hospital_name', type: 'varchar' }) // nullable，非必须
  hospitalName: string

  @Column({ name: 'case_path', type: 'varchar', nullable: true }) // nullable，非必须
  casePath: string

  @Column({ name: 'wave_page_info', type: 'varchar', nullable: true }) // type 数据库键类型
  wavePageInfo: string

  @CreateDateColumn({
    type: 'datetime',
    name: 'created_time',
    comment: '创建时间'
  })
  createdTime: Date

  @UpdateDateColumn({
    type: 'datetime',
    name: 'updated_time',
    comment: '更新时间'
  })
  updateTime: Date
}
