<template>
  <PageWrapper show-logo>
    <div class="absolute w-36 h-36 right-0 top-0" @click="onTopRightClick"></div>
    <div class="absolute w-36 h-36 left-0 top-0" @click="onTopLeftClick"></div>
    <div class="absolute w-36 h-36 left-0 bottom-0 z-50" @click="onBottomLeftClick"></div>
    <div class="content-wrapper flex-center h-[640px]">
      <div class="">
        <!-- <div class="flex justify-between mb-12">
          <div class="intro-item mr-12" @click="goPage('/introduction/company')">
            <span class="">企</span>
            <span class="">业</span>
            <span class="">简</span>
            <span class="">介</span>
          </div>
          <div class="intro-item" @click="goPage('/introduction/principle')">
            <span class="">工</span>
            <span class="">作</span>
            <span class="">原</span>
            <span class="">理</span>
          </div>
        </div>
        <div class="flex justify-between">
          <div class="intro-item mr-12" @click="goPage('/introduction/attention')">
            <span class="">注</span>
            <span class="">意</span>
            <span class="">事</span>
            <span class="">项</span>
          </div>
          <div class="intro-item" @click="goPage('/introduction/troubleshooting')">
            <span class="">故</span>
            <span class="">障</span>
            <span class="">排</span>
            <span class="">除</span>
          </div>
        </div> -->

        <div class="col-flex text-white">
          <button class="menu-item mb-12" @click="goPage('/introduction/company')">企 业 简 介</button>
          <button class="menu-item mb-12" @click="goPage('/introduction/principle')">工 作 原 理</button>
          <button class="menu-item" @click="goPage('/introduction/attention')">注 意 事 项</button>
        </div>
      </div>
    </div>
  </PageWrapper>
</template>

<script setup lang="ts">
import PageWrapper from '@/components/PageWrapper/index.vue'
import { useRouter } from 'vue-router';
import { reactive, watch, onBeforeUnmount } from 'vue';

const router = useRouter()

function goPage(path: string) {
  router.push({ path })
}

type IClickState = {
  one: boolean
  two: boolean
  three: boolean
}

const clickState = reactive<IClickState>({ one: false, two: false, three: false })

function onTopRightClick() {
  console.log('右上角点击了：', clickState)
  if (!clickState.one) clickState.one = true
  if (clickState.two) clickState.two = false
  if (clickState.three) clickState.three = false
  console.log('右上角状态变了：', clickState)
  window.electronAPI.serialport.dispatch('IntroChooseSend.TopRight')
}

function onTopLeftClick() {
  console.log('左上角点击了：', clickState)
  if (clickState.one && !clickState.three) clickState.two = true
  console.log('左上角状态变了：', clickState)
  window.electronAPI.serialport.dispatch('IntroChooseSend.TopLeft')
}

function onBottomLeftClick() {
  console.log('左下角点击了：', clickState)
  if (clickState.one && clickState.two) clickState.three = true
  console.log('左下角状态变了：', clickState)
  window.electronAPI.serialport.dispatch('IntroChooseSend.BottomLeft')
}

watch(clickState, (newState) => {
  if (newState.one && newState.two && newState.three) {
    router.push({ path: '/action/calibration/power' })
  }
})

onBeforeUnmount(() => {
  clickState.one = false
  clickState.two = false
  clickState.three = false
})
</script>
