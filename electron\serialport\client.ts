import { ipc<PERSON><PERSON><PERSON>, clipboard } from 'electron'
import { ClientInterface } from './types'

import type { IpcRendererEvent } from 'electron'

const SERIALPORT_CHANNEL_KEY = 'serialport-channel'

const dataInterface: ClientInterface = {
  dispatch,
  receive,
  removeListener,
  copyToClipboard,
  readFromClipboard,
  quitApp
}

export default dataInterface

async function dispatch(callName: string, ...args: ReadonlyArray<unknown>) {
  return ipcRenderer.invoke(SERIALPORT_CHANNEL_KEY, callName, ...args)
}

function receive(
  channel: string,
  listener: (event: IpcRendererEvent, ...args: any[]) => void
) {
  ipcRenderer.on(channel, listener)
}

function removeListener(channel: string) {
  ipcRenderer.removeAllListeners(channel)
}

function copyToClipboard(text: string) {
  // 将文本复制到系统剪贴板中
  clipboard.writeText(text)
}

function readFromClipboard(): string {
  return clipboard.readText()
}

function quitApp(): void {
  ipcRenderer.invoke('quit-app')
}
