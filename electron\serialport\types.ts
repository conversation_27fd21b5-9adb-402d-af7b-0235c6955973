import type {
  TemperatureCalibrationChooseMethods,
  TemperatureCalibrationPageOneMethods,
  TemperatureCalibrationPageTwoMethods,
  TemperatureCalibrationPageThreeMethods,
  TemperatureCalibrationPageFourMethods
} from '../instructions/temperature-calibration'
import type { WavePageMethods } from '../instructions/wave-page'
import type { PowerCalibrationPageOneSendMethods } from '../instructions/power-calibration'
import type { PrescriptionSendMethods } from '../instructions/prescription'
import type { WaveformSelectionMethods } from '../instructions/wave-selection'
import type { ActionChooseMethods } from '../instructions/action-choose'
import type { IntroChooseSendMethods } from '../instructions/intro-choose'
import type { StartingUpMethods } from '../instructions/staring-up'
import type { PasswordValidateMethods } from '../instructions/password-validate'

import type { IpcRendererEvent } from 'electron'
import type { ElectronLog, LogLevel } from 'electron-log'

export type InitializeOptions = Readonly<{
  configDir: string
  key: string
  logger: ElectronLog
}>

export type Methods =
  | 'open' // 打开串口连接
  | StartingUpMethods // 关闭串口连接
  | PasswordValidateMethods
  | IntroChooseSendMethods
  | ActionChooseMethods
  | WaveformSelectionMethods
  | PrescriptionSendMethods
  | PowerCalibrationPageOneSendMethods
  | TemperatureCalibrationChooseMethods
  | TemperatureCalibrationPageOneMethods
  | TemperatureCalibrationPageTwoMethods
  | TemperatureCalibrationPageThreeMethods
  | TemperatureCalibrationPageFourMethods
  | WavePageMethods
  | CurrentPageMethod

export type WorkerRequest = Readonly<
  | {
      type: 'open'
    }
  | {
      type: 'close'
    }
  | {
      type: 'send'
      method: Methods
      args: ReadonlyArray<unknown>
    }
>

export type WrappedWorkerRequest = Readonly<{
  seq: number
  request: WorkerRequest
}>

export type WrappedWorkerLogEntry = Readonly<{
  type: 'log'
  level: LogLevel
  args: ReadonlyArray<unknown>
}>

export type WrappedWorkerResponse =
  | Readonly<{
      type: 'response'
      seq: number
      error: string | undefined
      renderCallName?: string
      response: unknown
    }>
  | WrappedWorkerLogEntry

export type DataInterface = {
  dispatch: (callName: string, ...args: ReadonlyArray<unknown>) => Promise<any>
}

export type ClientInterface = DataInterface & {
  receive: (
    channel: string,
    listener: (event: IpcRendererEvent, ...args: any[]) => void
  ) => void
  removeListener: (channel: string) => void
  copyToClipboard: (text: string) => void
  readFromClipboard: () => string
  quitApp: () => void
}

export type ServerInterface = DataInterface & {
  // Server-only
  open: () => Promise<void>
}

// 程序变量
export type ReceiveData = {
  /******** 首页 ********/
  // 主控板版本前4位
  mainboardPrefix: string
  // 主控板版本后4位
  mainboardSuffix: string

  /******** 波形界面 ********/
  // 功率显示
  power: string
  // 周期
  period: string
  // 占空比
  dutyRatio: string
  // 温度T1显示
  temperatureOne: number[]
  // 温度T1是否选择
  temperatureOneSelected: string
  // 温度T2
  temperatureTwo: number[]
  // 温度T2是否选择
  temperatureTwoSelected: string
  // 音量
  volume: string
  // 处方号
  prescriptionId: string
  // 是否静音
  mute: string
  // 是否锁屏
  lockscreen: string
  // 实时功率
  realtimePower: string
  // 辐射器
  radiator: string
  // 辐射器是否选中
  radiatorSelected: string
  // 测温传感器故障
  sensorFault: string
  // 测温通讯故障
  communicationFault: string
  // 倒计时
  countdown: string
  // 指示灯
  light: string
  // 以下为波形界面新增指令
  // 温度T3显示
  temperatureThree: number[]
  // 温度T3是否选择
  temperatureThreeSelected: string
  // 温度T4
  temperatureFour: number[]
  // 温度T4是否选择
  temperatureFourSelected: string
  // 设定温度显示
  settingTemperature: string

  // 校准界面
  // 功率标签值显示
  powerLabel: string
  // 功率DA数据显示
  powerDA: string
  // 功率调整步进
  powerStep: string
}

export type CurrentPage =
  | 'ActionChoose' // 首页
  | 'WavePage' // 波形界面
  | 'WavePage.RealtimePower' // 连续波带实时功率
  | 'TemperatureCalibrationPageOneSend' // T1温度校准界面
  | 'TemperatureCalibrationPageTwoSend' // T2温度校准界面
  | 'TemperatureCalibrationPageThreeSend' // T3温度校准界面
  | 'TemperatureCalibrationPageFourSend' // T4温度校准界面
  | 'PowerCalibrationPageOneSend' // 功率校准界面

type CurrentPageMethod =
  | 'CurrentPage.ActionChoose' // 首页
  | 'CurrentPage.WavePage' // 波形界面
  | 'CurrentPage.WavePage.RealtimePower' // 连续波带实时功率
  | 'CurrentPage.TemperatureCalibrationPageOneSend' // T1温度校准界面
  | 'CurrentPage.TemperatureCalibrationPageTwoSend' // T2温度校准界面
  | 'CurrentPage.TemperatureCalibrationPageThreeSend' // T3温度校准界面
  | 'CurrentPage.TemperatureCalibrationPageFourSend' // T4温度校准界面
  | 'CurrentPage.PowerCalibrationPageOneSend' // 功率校准界面
