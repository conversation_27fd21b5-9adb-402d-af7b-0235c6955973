import type { LogFunctions } from 'electron-log'
import Basic from './entities/basic.entity'
import Patient from './entities/patient.entity'
import PatientCase from './entities/patient-case.entity'
import User from './entities/user.entity'
import { UserRole } from './entities/user.entity'
import type { InsertResult, UpdateResult, FindOptionsOrder } from 'typeorm'

export type UserQueryCriteria = {
  isSuper?: boolean
  keyword?: string
  role?: UserRole
  order?: FindOptionsOrder<User>
}

export type UserLoginParams = {
  loginName: string
  password: string
}

export type BaseResult = {
  success: boolean
  message: string
}

export type UserLoginResult = BaseResult & {
  user?: User
}

export type ResetUserPasswordParams = {
  userId: number
  newPassword: string
}

export type UpdateUserPasswordParams = {
  currentPassword: string
} & ResetUserPasswordParams

export type PatientQueryCriteria = {
  keyword?: string
  userId: number
  role: UserRole
  order?: FindOptionsOrder<User>
}

export type DataInterface = {
  close: () => Promise<void>
  getBasicInfo: () => Promise<Basic>
  initBasicInfo: () => Promise<Basic | InsertResult>
  updateBasicInfo: (basic: Basic) => Promise<Basic>
  checkCasePath: () => Promise<void>
  // 患者接口
  queryPatients: (params?: PatientQueryCriteria) => Promise<[Patient[], number]>
  createPatient: (patient: Patient) => Promise<BaseResult & { data?: Patient }>
  updatePatient: (patient: Patient) => Promise<BaseResult & { data?: Patient }>
  deletePatient: (patientId: number) => Promise<UpdateResult>
  // syncStoragePatients: () => Promise<unknown>

  // 病历接口
  queryPatientCases: (patientId: number) => Promise<PatientCase[]>
  createPatientCase: (
    patientId: number,
    patientCase: PatientCase
  ) => Promise<BaseResult>
  updatePatientCase: (
    patientId: number,
    patientCase: PatientCase
  ) => Promise<BaseResult>
  deletePatientCase: (patientCaseId: number) => Promise<UpdateResult>

  // 用户接口
  queryUsers: (queryCriteria: UserQueryCriteria) => Promise<User[]>
  createUser: (user: User) => Promise<BaseResult>
  updateUser: (user: User) => Promise<BaseResult>
  deleteUser: (userId: number) => Promise<UpdateResult>
  userLogin: (params: UserLoginParams) => Promise<UserLoginResult>
  updateUserPassword: (params: UpdateUserPasswordParams) => Promise<BaseResult>
  resetUserPassword: (params: ResetUserPasswordParams) => Promise<BaseResult>
}

export type ClientInterface = DataInterface & {
  exportPdf: (...args: any[]) => void
  removePdf: (...args: any[]) => void
  exportExcel: (...args: any[]) => void
  removeExcel: (...args: any[]) => void
}

export type ServerInterface = DataInterface & {
  // Server-only

  initialize: (options: {
    configDir: string
    logger: Omit<LogFunctions, 'log'>
  }) => Promise<void>
}
