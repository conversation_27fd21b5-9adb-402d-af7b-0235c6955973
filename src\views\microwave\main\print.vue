<template>
  <div id="pdfPreviewWrapper" class="pdf_preview_wrapper">
    <div v-for="page in pdfState.pages" :key="page" class="pdf-canvas"
      :class="[page !== pdfState.pages ? 'break-page' : '']">
      <canvas :id="`pdf-canvas-${page}`"></canvas>
    </div>

    <div v-if="show" class="flex justify-center items-center print-btn">
      <el-button type="warning" size="large" @click="handleBack">返回</el-button>
      <el-button type="primary" size="large" @click="handleExport">确认打印</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive, nextTick } from 'vue';
import { useRouter } from 'vue-router';

// @ts-ignore
import * as pdfjsLib from 'pdfjs-dist'
import 'pdfjs-dist/web/pdf_viewer.css';

import { useMedicalStore } from '@/store/modules/medical'

defineOptions({
  name: 'PatientCasePrintPreview'
})

const medicalStore = useMedicalStore()

const router = useRouter()

onMounted(() => {
  console.log(`medicalStore.getPdfContent : `, medicalStore.getPdfContent)
  if (medicalStore.getPdfContent) renderPdf(medicalStore.getPdfContent)
})

const show = ref(true)

function savePrintParams() {
  localStorage.setItem('print-params', JSON.stringify(router.currentRoute.value.query))
}

function handleBack() {
  // savePrintParams()
  const { patientId } = router.currentRoute.value.query
  router.replace({ path: '/patient-case/management', query: { patientId } })
}

function handleExport() {
  show.value = false
  window.print();
  nextTick(() => {
    show.value = true
  })
  window.onafterprint = () => {
    // savePrintParams()
    router.back()
  }
}

const pdfState = reactive({
  pages: 0,
  // scale: 1.0,
  scale: 1.2,
})
async function renderPdf(base64: string) {
  // 将 Base64 转为 Uint8Array
  console.log('pdf data uri : ', base64)

  // @ts-ignore
  let pdfjsWorker = await import('pdfjs-dist/build/pdf.worker.entry.js');

  pdfjsLib.GlobalWorkerOptions.workerSrc = pdfjsWorker
  console.log('pdf js lib : ', pdfjsLib)

  // 加载和渲染 PDF 文件
  pdfjsLib.getDocument(base64).promise.then((pdf) => {
    const total = pdf.numPages
    pdfState.pages = pdf.numPages
    console.log('pdf numbers : ', total)

    for (let pageNumber = 1; pageNumber <= total; pageNumber++) {
      pdf.getPage(pageNumber).then((page) => {
        console.log('Page loaded');

        const viewport = page.getViewport({ scale: pdfState.scale });

        // Prepare canvas using PDF page dimensions
        const canvas = document.getElementById(`pdf-canvas-${pageNumber}`) as HTMLCanvasElement | null;
        console.log('canvas instance : ', canvas)
        if (canvas !== null) {
          const context = canvas.getContext('2d');
          if (context) {
            canvas.height = viewport.height;
            canvas.width = viewport.width;

            // Render PDF page into canvas context
            const renderContext = {
              canvasContext: context,
              viewport: viewport
            };
            const renderTask = page.render(renderContext);
            renderTask.promise.then(() => {
              console.log(`Page ${pageNumber} rendered`);
            });
          }
        }
      });
    }
  });
}
</script>

<style>
@media print {
  .vue-notification-group {
    display: none;
  }
}
</style>
<style scoped>
@media print {

  @page {
    size: portrait;

    /* 去掉页眉页脚*/
    margin-top: 0;
    margin-bottom: 0;

    /* 左右边距 */
    margin-left: 0;
    margin-right: 0;
  }

  body {
    size: auto;
    margin: 0;
    padding: 0;
  }

  .print-btn {
    display: none;
  }

  .break-page {
    page-break-after: auto;
  }

  .vue-notification-group {
    display: none;
  }
}

.pdf_preview_wrapper {
  padding: 0;
  background-color: #ededf0;
  /* height: 100%; */
  display: flex;
  flex-direction: column;
  padding-bottom: 100px;
}

.print-btn {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 40px;
  padding-top: 50px;
  padding-bottom: 50px;
  background-color: #fff;
}

.save_wraper {
  position: fixed;
  top: 0;
  left: 20px;
  z-index: 9;
  display: flex;
  justify-content: flex-end;
  margin: 0 -20px;
  padding: 5px 30px 5px 0;
  width: 100%;
  background: #f9f9fa;
}

.page {
  margin: 1px auto -8px;
  width: 592px;
  height: 842px;
  border: 9px solid transparent;
  /* border-image: url(@/assets/images/border-shadow.png) 9 9 repeat; */
  background-color: #fff;
  background-clip: content-box;
}

.page_wrapper {
  position: relative;
  display: flex;
  flex-direction: column;
  padding: 50px 50px 30px;
  height: 100%;
}

.title {
  width: 100%;
  text-align: center;
  margin-top: 10px;
  font-size: 24px;
  padding-bottom: 10px;
  border-bottom: 1px solid #333;
  font-weight: 600;
  display: inline-flex;
  align-self: center;
  justify-content: center;
}

#chart {
  height: 400px;
}

.butnew_1,
.butnew_2 {
  background: #0AA8AC;
  color: #000;
  font-size: 16px;
  border-radius: 100px;
  border: none;
  margin: 20px 0 0;
}

.butnew_2 {
  background: #FBBD23;
  margin: 20px 20px 0 0;
}

.pdf-viewer {
  width: 95vw;
  flex: 1;
  margin: 0 auto;
  border: 1px solid #f90;
  overflow: auto;
}

.pdf-viewer.hide-border {
  border-color: transparent;
  border: none;
}

.pdf-canvas {
  margin: 20px auto 0;
  display: flex;
  justify-content: center;
}

.pdf-canvas:first-child {
  margin-top: 0;
}
</style>
