import { ipcMain } from 'electron'

type SerialportType = {
  serialportCall(callName: string, args: <PERSON>onlyArray<unknown>): unknown
}

let initialized = false

let serialport: SerialportType | undefined

const SERIALPORT_CHANNEL_KEY = 'serialport-channel'

export function initialize(mainSerialport: SerialportType): void {
  if (initialized) {
    throw new Error('serialport : already initialized!')
  }
  initialized = true

  serialport = mainSerialport

  // 注册监听事件
  ipcMain.handle(
    SERIALPORT_CHANNEL_KEY,
    async (_, callName: string, ...args) => {
      if (!serialport) {
        throw new Error(
          `${SERIALPORT_CHANNEL_KEY}: serialport not yet initialized!`
        )
      }

      return serialport.serialportCall(callName, args || [])
    }
  )
}
