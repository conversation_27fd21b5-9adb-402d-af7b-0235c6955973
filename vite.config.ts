import { rmSync, copyFileSync, mkdirSync, existsSync } from 'node:fs'
import { resolve, dirname } from 'node:path'
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import electron from 'vite-plugin-electron'
import pkg from './package.json' assert { type: 'json' }
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import copy from 'rollup-plugin-copy'
import { viteCommonjs } from '@originjs/vite-plugin-commonjs'
import { viteStaticCopy } from 'vite-plugin-static-copy'

function pathResolve(dir: string) {
  return resolve(process.cwd(), '.', dir)
}

// 开发环境下手动复制 DICOM Worker 文件的插件
function copyDicomWorkerFiles() {
  return {
    name: 'copy-dicom-worker-files',
    configureServer() {
      // 延迟复制，确保在依赖优化完成后执行
      setTimeout(() => {
        copyWorkerFiles()
      }, 2000)
    },
    buildStart() {
      // 在构建开始时也复制一次
      copyWorkerFiles()
    }
  }
}

function copyWorkerFiles() {
  const sourceWorker = resolve(
    __dirname,
    'node_modules/@cornerstonejs/dicom-image-loader/dist/esm/decodeImageFrameWorker.js'
  )
  const sourceShared = resolve(
    __dirname,
    'node_modules/@cornerstonejs/dicom-image-loader/dist/esm/shared'
  )
  const targetDir = resolve(__dirname, 'node_modules/.vite/deps')
  const targetWorker = resolve(targetDir, 'decodeImageFrameWorker.js')
  const targetShared = resolve(targetDir, 'shared')

  try {
    // 确保目标目录存在
    if (!existsSync(targetDir)) {
      mkdirSync(targetDir, { recursive: true })
    }

    // 复制 worker 文件
    if (existsSync(sourceWorker)) {
      copyFileSync(sourceWorker, targetWorker)
      console.log('✅ 已复制 DICOM Worker 文件到:', targetWorker)
    }

    // 复制 shared 目录
    if (existsSync(sourceShared)) {
      copySharedDirectory(sourceShared, targetShared)
      console.log('✅ 已复制 DICOM Shared 目录到:', targetShared)
    }
  } catch (error) {
    console.error('❌ 复制 DICOM Worker 文件失败:', error)
  }
}

function copySharedDirectory(src: string, dest: string) {
  if (!existsSync(dest)) {
    mkdirSync(dest, { recursive: true })
  }

  const fs = require('fs')
  const items = fs.readdirSync(src)

  for (const item of items) {
    const srcPath = resolve(src, item)
    const destPath = resolve(dest, item)

    if (fs.statSync(srcPath).isDirectory()) {
      copySharedDirectory(srcPath, destPath)
    } else {
      copyFileSync(srcPath, destPath)
    }
  }
}

// https://vitejs.dev/config/
export default defineConfig(({ command }) => {
  rmSync('dist-electron', { recursive: true, force: true })

  const isServe = command === 'serve'
  const isBuild = command === 'build'
  const sourcemap = isServe || !!process.env.VSCODE_DEBUG

  return {
    resolve: {
      alias: [
        // @/xxxx => src/xxxx
        {
          find: /@\//,
          replacement: pathResolve('src') + '/'
        },
        {
          find: /@db\//,
          replacement: pathResolve('db') + '/'
        },
        // 在开发模式下，当 @cornerstonejs/dicom-image-loader 尝试加载 './decodeImageFrameWorker.js' 时，
        // Vite 会被这个别名拦截，并提供正确的 worker 文件路径。
        // 这解决了开发服务器下找不到 worker 的问题。
        // 注意：我们只在 'serve' (dev) 模式下应用这个别名。
        !isBuild
          ? {
              find: './decodeImageFrameWorker.js',
              replacement: resolve(
                __dirname,
                'node_modules/@cornerstonejs/dicom-image-loader/dist/esm/decodeImageFrameWorker.js'
              )
            }
          : undefined
      ]
    },
    plugins: [
      vue(),
      AutoImport({
        resolvers: [ElementPlusResolver()]
      }),
      Components({
        resolvers: [ElementPlusResolver()]
      }),
      // for dicom-parser
      viteCommonjs({
        include: ['dicom-parser']
      }),
      // 开发环境下手动复制 DICOM Worker 文件
      ...(isServe ? [copyDicomWorkerFiles()] : []),
      // 生产环境使用 viteStaticCopy
      ...(isBuild
        ? [
            viteStaticCopy({
              targets: [
                {
                  src: 'node_modules/@cornerstonejs/dicom-image-loader/dist/esm/decodeImageFrameWorker.js',
                  dest: 'assets'
                },
                {
                  src: 'node_modules/@cornerstonejs/dicom-image-loader/dist/esm/shared',
                  dest: 'assets'
                }
              ]
            })
          ]
        : []),
      electron([
        {
          // Main-Process entry file of the Electron App.
          entry: 'electron/main/index.ts',
          onstart(options) {
            options.startup()
          },
          vite: {
            build: {
              sourcemap,
              minify: isBuild,
              outDir: 'dist-electron/main',
              rollupOptions: {
                external: [
                  ...Object.keys('dependencies' in pkg ? pkg.dependencies : {}),
                  'electron/serialport/sqlite.worker.ts'
                ]
              }
            }
          }
        },
        {
          entry: 'electron/preload/index.ts',
          onstart(options) {
            // Notify the Renderer-Process to reload the page when the Preload-Scripts build is complete,
            // instead of restarting the entire Electron App.
            options.reload()
          },
          vite: {
            build: {
              sourcemap,
              minify: isBuild,
              outDir: 'dist-electron/preload',
              rollupOptions: {
                external: Object.keys(
                  'dependencies' in pkg ? pkg.dependencies : {}
                )
              }
            }
          }
        },
        {
          entry: 'electron/db/sqlite.worker.ts',
          vite: {
            build: {
              minify: isBuild,
              outDir: 'dist-electron/main',
              rollupOptions: {
                external: [
                  ...Object.keys('dependencies' in pkg ? pkg.dependencies : {})
                ]
              }
            }
          }
        }
      ])
      // Use Node.js API in the Renderer-process
      // renderer({
      //   nodeIntegration: true
      // })
      // commonjs()
    ],
    server: process.env.VSCODE_DEBUG
      ? (() => {
          const url = new URL(pkg.debug.env.VITE_DEV_SERVER_URL)
          return {
            host: url.hostname,
            port: +url.port
          }
        })()
      : {
          port: 35173
        },
    clearScreen: false,
    build: {
      rollupOptions: {
        plugins: [
          copy({
            targets: [
              {
                src: [
                  'public/SourceHanSansCN-Bold.otf',
                  'public/SourceHanSansCN-Normal.otf'
                ],
                dest: 'dist/assets/'
              },
              {
                src: 'public/SourceHanSansCN-Bold.otf',
                dest: 'dist/assets/'
              }
            ]
          })
        ]
      }
    },
    worker: {
      format: 'es',
      rollupOptions: {
        external: ['@icr/polyseg-wasm']
      }
    },
    assetsInclude: ['**/*.wasm'],
    // seems like only required in dev mode
    optimizeDeps: {
      include: ['dicom-parser'],
      exclude: ['@cornerstonejs/dicom-image-loader']
    }
  }
})
