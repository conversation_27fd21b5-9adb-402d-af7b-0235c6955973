import {
  ref,
  reactive,
  onMounted,
  onUnmounted,
  watch,
  computed,
  nextTick
} from 'vue'
import { useRouter } from 'vue-router'
import { useMedicalStore } from '@/store/modules/medical'
import { useBottomWarnNotify, closeNotify } from '@/utils/toast'
import { formatTemperature } from '@/utils'
import { onLongPress } from '@vueuse/core'
import { radiatorNameMap } from '@/views/action/cure/common/radiator'

import type { SerialportType, DrawFunc, GenerateDataFunc } from './types'
import type { ICureParams, RealtimeChartData } from '@/types/medical'

import { type MessageHandler } from 'element-plus'

type IWavePageType = 'pulse' | 'sine' | 'triangle' | 'continuation'
export type UseWavePageOptions = {
  drawFunc?: DrawFunc
  generateChartData: GenerateDataFunc
  drawInterval: number
  type: IWavePageType
}

export function useWavePage({
  drawFunc,
  generateChartData,
  drawInterval,
  type
}: UseWavePageOptions) {
  // 获取store
  const medicalStore = useMedicalStore()

  // 面板ref
  const panelRef = ref()
  // 绘制波形图相关
  let timer: NodeJS.Timeout | null = null
  const drawCount = ref(0)
  const waveChartData = ref<[number, number][]>([])
  const defaultDrawFunc: DrawFunc = () => {
    waveChartData.value = generateChartData()

    drawCount.value += 10
  }
  const draw = drawFunc || defaultDrawFunc

  const router = useRouter()

  // 治疗参数
  const parameters = reactive({
    realtimePower: 0,
    power: 0,
    time: '',
    countdown: '120:00',
    period: 5,
    dutyRatio: 50,
    prescriptionId: 0,
    volume: 15,
    temperature: 0,
    temperatureOne: '0',
    temperatureTwo: '0',
    temperatureThree: '0',
    temperatureFour: '0',
    temperatureOneSelected: 0,
    temperatureTwoSelected: 0,
    temperatureThreeSelected: 0,
    temperatureFourSelected: 0,
    radiator: 0,
    radiatorSelected: 1,
    sensorFault: 0,
    communicationFault: 0,
    light: false,
    settingTemperature: ''
  })

  // 全局状态（双向绑定）
  const state = reactive({
    boot: false,
    pause: false,
    mute: false,
    lockscreen: false
  })

  // 页面底部信息
  const touchScreenInfo = reactive({
    version: '',
    name: ''
  })
  const touchScreenVersionNameArr = computed(() => {
    if (touchScreenInfo.name && touchScreenInfo.name.length > 0) {
      return touchScreenInfo.name.split('')
    }

    return []
  })

  const loading = ref(true)

  // initParamater()
  window.electronAPI.serialport.dispatch('CurrentPage.WavePage')

  // 实时功率绘制相关
  let realtimePowerTimer: NodeJS.Timer | undefined
  const realtimePowerData = ref<number[][]>([])
  const realtimeChartData = ref<RealtimeChartData>({
    power: [],
    temperatureOne: [],
    temperatureTwo: [],
    temperatureThree: [],
    temperatureFour: []
  })
  const realtimeChartRef = ref()
  function drawRealtimePower() {
    const timeArr = parameters.countdown.split(':')
    console.log(
      'parameters.countdown : ',
      parameters.time,
      parameters.countdown,
      timeArr
    )
    const maxTime = parseInt(parameters.time) * 60
    let seconds = maxTime
    if (timeArr && timeArr.length) {
      seconds = Number(timeArr[0]) * 60 + Number(timeArr[1])
    }
    // 功率数据
    const powerData = [maxTime - seconds, parameters.power]
    // 绘制功率曲线的同时，记录温度数据
    // 如果选择温度T1，记录T1数据
    // 温度T1数据
    let temperatureOneData = []
    if (parameters.temperatureOneSelected) {
      temperatureOneData = [
        maxTime - seconds,
        parseFloat(parameters.temperatureOne)
      ]
    } else {
      temperatureOneData = [maxTime - seconds, undefined as unknown as number]
    }
    // 温度T2数据
    let temperatureTwoData = []
    if (parameters.temperatureTwoSelected) {
      temperatureTwoData = [
        maxTime - seconds,
        parseFloat(parameters.temperatureTwo)
      ]
    } else {
      temperatureTwoData = [maxTime - seconds, undefined as unknown as number]
    }
    // 温度T3数据
    let temperatureThreeData = []
    if (parameters.temperatureThreeSelected) {
      temperatureThreeData = [
        maxTime - seconds,
        parseFloat(parameters.temperatureThree)
      ]
    } else {
      temperatureThreeData = [maxTime - seconds, undefined as unknown as number]
    }
    // 温度T4数据
    let temperatureFourData = []
    if (parameters.temperatureFourSelected) {
      temperatureFourData = [
        maxTime - seconds,
        parseFloat(parameters.temperatureFour)
      ]
    } else {
      temperatureFourData = [maxTime - seconds, undefined as unknown as number]
    }

    console.log(
      'draw realtime power : ',
      state.boot,
      parameters.power,
      parameters.countdown
    )
    if (state.boot) {
      const lastPowerData =
        realtimeChartData.value.power[realtimeChartData.value.power.length - 1]
      const lastPower = lastPowerData[1]
      if (
        !(
          (parameters.countdown === '0:01' ||
            parameters.countdown === '0:02') &&
          parameters.power === 0 &&
          lastPower !== 0
        )
      ) {
        realtimeChartData.value.power = [
          ...realtimeChartData.value.power,
          powerData
        ]
      }

      // 存入温度T1数据
      realtimeChartData.value.temperatureOne = [
        ...realtimeChartData.value.temperatureOne,
        temperatureOneData
      ]

      // 存入温度T2数据
      realtimeChartData.value.temperatureTwo = [
        ...realtimeChartData.value.temperatureTwo,
        temperatureTwoData
      ]

      // 存入温度T3数据
      realtimeChartData.value.temperatureThree = [
        ...realtimeChartData.value.temperatureThree,
        temperatureThreeData
      ]

      // 存入温度T4数据
      realtimeChartData.value.temperatureFour = [
        ...realtimeChartData.value.temperatureFour,
        temperatureFourData
      ]
    }
  }

  function wrapDraw() {
    draw()
    if (timer) {
      clearTimeout(timer)
    }
    timer = setTimeout(wrapDraw, drawInterval * parameters.period)
  }

  // 开始绘制
  function startDraw() {
    if (timer) {
      clearTimeout(timer)
      timer = null
      drawCount.value = 0
    }
    wrapDraw()

    if (realtimePowerTimer) {
      // 实时功率
      // @ts-ignore
      clearInterval(realtimePowerTimer)
    }

    realtimePowerTimer = setInterval(() => {
      drawRealtimePower()
    }, 1000)
  }

  // 清楚绘制定时器
  function clearTimer() {
    if (timer) {
      clearTimeout(timer)
      timer = null
      drawCount.value = 0
    }

    // 实时功率
    if (realtimePowerTimer) {
      // @ts-ignore
      clearInterval(realtimePowerTimer)
      realtimePowerTimer = undefined

      if (!state.pause) {
        if (realtimePowerData.value.length) {
          const lastRealtimePowerData =
            realtimePowerData.value[realtimePowerData.value.length - 1]
          if (
            lastRealtimePowerData &&
            lastRealtimePowerData.length &&
            lastRealtimePowerData[1] === 0
          ) {
            realtimePowerData.value.splice(
              realtimePowerData.value.length - 1,
              1
            )
            realtimePowerData.value = [...realtimePowerData.value]
          }
        }
      }
    }
  }

  // 清除告警timer
  function clearFaultTimer() {
    if (isInFaultTimer) {
      clearTimeout(isInFaultTimer)
      isInFaultTimer = undefined
    }
  }

  let isInFaultTimer: NodeJS.Timeout | undefined
  function handleIsInFault() {
    console.log(
      'handle fault from medical : ',
      medicalStore.getSensorFault,
      medicalStore.getCommunicationFault
    )
    if (medicalStore.getSensorFault) {
      parameters.sensorFault = 1
    }

    if (medicalStore.getCommunicationFault) {
      parameters.communicationFault = 1
    }
  }

  // 初始化数据接收
  function initReceive() {
    // 开启波形界面循环解析
    // window.electronAPI.serialport.dispatch('CurrentPage.WavePage')

    // 监听串口解析数据
    window.electronAPI.serialport.receive(
      'wave-page-display',
      (
        _,
        {
          power,
          period,
          dutyRatio,
          temperatureOne,
          temperatureTwo,
          temperatureOneSelected,
          temperatureTwoSelected,
          volume,
          prescriptionId,
          mute,
          lockscreen,
          realtimePower,
          radiator,
          radiatorSelected,
          sensorFault,
          communicationFault,
          countdown,
          light,
          // 新增接收数据
          temperatureThree,
          temperatureFour,
          temperatureThreeSelected,
          temperatureFourSelected,
          settingTemperature
        }: SerialportType
      ) => {
        // console.log('temperature display : ', temperatureOne, temperatureTwo, temperatureThree, temperatureFour)
        if (power !== undefined) {
          parameters.power = power
        }

        if (period !== undefined) {
          console.log('period : ', period)
          parameters.period = period
        }

        if (dutyRatio !== undefined) {
          parameters.dutyRatio = dutyRatio
        }

        if (temperatureOne !== undefined) {
          parameters.temperatureOne = formatTemperature(temperatureOne)
        }

        if (temperatureTwo !== undefined) {
          parameters.temperatureTwo = formatTemperature(temperatureTwo)
        }

        if (temperatureOneSelected !== undefined) {
          console.log('temperatureOneSelected : ', temperatureOneSelected)
          parameters.temperatureOneSelected = temperatureOneSelected
        }

        if (temperatureTwoSelected !== undefined) {
          parameters.temperatureTwoSelected = temperatureTwoSelected
        }

        if (volume !== undefined) {
          console.log('volume : ', volume)
          parameters.volume = volume
        }

        if (prescriptionId !== undefined) {
          parameters.prescriptionId = prescriptionId
        }

        if (mute !== undefined) {
          state.mute = !!mute
        }

        if (lockscreen !== undefined) {
          console.log('receive lockscreen : ', lockscreen)
          state.lockscreen = !!lockscreen
        }

        if (realtimePower !== undefined) {
          parameters.realtimePower = realtimePower
        }

        if (radiator !== undefined) {
          parameters.radiator = radiator
          if (radiator === 0 && parameters.radiatorSelected) {
            state.boot = false
          }
        }

        if (radiatorSelected !== undefined) {
          parameters.radiatorSelected = radiatorSelected
        }

        if (sensorFault !== undefined) {
          console.log('sensorFault: ', sensorFault)
          parameters.sensorFault = sensorFault
          if (sensorFault) {
            state.boot = false
          }
        }

        if (communicationFault !== undefined) {
          parameters.communicationFault = communicationFault
          if (communicationFault) {
            state.boot = false
          }
        }

        if (countdown !== undefined) {
          const old = parameters.countdown
          // console.log(
          //   'countdown : ',
          //   countdown,
          //   parameters.countdown,
          //   parameters.countdown == '0:01',
          //   parseInt(old.replace(/:/, '')) === 1
          // )
          if (parseInt(old.replace(/:/, '')) === 1) {
            state.boot = false
            nextTick(() => {
              compensationData()
            })
          }
          parameters.countdown = countdown

          if (!state.boot && !state.pause) {
            // parameters.time = parseInt(countdown)
            parameters.time = countdown
          }
        }

        if (light !== undefined) {
          parameters.light = !!light
        }

        if (temperatureThree !== undefined) {
          parameters.temperatureThree = formatTemperature(temperatureThree)
        }

        if (temperatureFour !== undefined) {
          parameters.temperatureFour = formatTemperature(temperatureFour)
        }

        if (temperatureThreeSelected !== undefined) {
          console.log('temperatureThreeSelected : ', temperatureThreeSelected)
          parameters.temperatureThreeSelected = temperatureThreeSelected
        }

        if (temperatureFourSelected !== undefined) {
          parameters.temperatureFourSelected = temperatureFourSelected
        }

        if (settingTemperature !== undefined) {
          console.log('settingTemperature : ', settingTemperature)
          parameters.settingTemperature = formatTemperature(
            settingTemperature
          ).replace(/0$/, '')
        }
      }
    )
  }

  // 页面挂载完成逻辑
  onMounted(() => {
    // 处方选择跳转逻辑处理
    if (router.currentRoute.value.query.time) {
      parameters.time = router.currentRoute.value.query.time as string
    } else {
      parameters.time = ''
    }

    if (router.currentRoute.value.query.id) {
      parameters.prescriptionId = Number(router.currentRoute.value.query.id)
    } else {
      parameters.prescriptionId = 0
    }

    initReceive()

    setTimeout(() => {
      loading.value = false
    }, 1000)

    // console.log(
    //   'wave page mounted ((((((((((((((((((((((((((((((((((((((((((((((((((((((((()))))))))))))))))))))))))))))))))))))))))))))))))))))'
    // )
    if (isInFaultTimer) {
      clearTimeout(isInFaultTimer)
      isInFaultTimer = undefined
    }
    isInFaultTimer = setTimeout(() => {
      handleIsInFault()
    }, 1000)

    // 获取页面底部信息
    window.electronAPI.sqlite.getBasicInfo().then(res => {
      if (res) {
        touchScreenInfo.version = res.touchScreenVersion
        touchScreenInfo.name = res.touchScreenVersionName
      }
    })
  })

  watch(router.currentRoute, route => {
    // console.log(
    //   'wave page route change ((((((((((((((((((((((((((((((((((((((((((((((((((((((((()))))))))))))))))))))))))))))))))))))))))))))))))))))'
    // )
    if (isInFaultTimer) {
      clearTimeout(isInFaultTimer)
      isInFaultTimer = undefined
    }
    isInFaultTimer = setTimeout(() => {
      handleIsInFault()
    }, 1000)
  })

  // 页面卸载逻辑
  onUnmounted(() => {
    console.log(
      'wave page unmounted ((((((((((((((((((((((((((((((((((((((((((((((((((((((((()))))))))))))))))))))))))))))))))))))))))))))))))))))'
    )
    clearTimer()
    drawCount.value = 0

    closeSensorFaultMessage()

    closeCommunicationFaultMessage()

    closeLockscreenMessage()

    clearFaultTimer()

    window.electronAPI.serialport.removeListener('wave-page-display')
  })

  // 治疗结束后，补偿数据的方法
  function compensationData() {
    console.log('compensation data : ', parameters.time)

    if (realtimeChartData.value.power?.length) {
      const lastDataArr =
        realtimeChartData.value.power[realtimeChartData.value.power.length - 1]
      const lastTime = lastDataArr[0]
      const lastPower = lastDataArr[1]
      const lastTemperatureOne =
        realtimeChartData.value.temperatureOne[
          realtimeChartData.value.temperatureOne.length - 1
        ][1]
      const lastTemperatureTwo =
        realtimeChartData.value.temperatureTwo[
          realtimeChartData.value.temperatureTwo.length - 1
        ][1]
      const lastTemperatureThree =
        realtimeChartData.value.temperatureThree[
          realtimeChartData.value.temperatureThree.length - 1
        ][1]
      const lastTemperatureFour =
        realtimeChartData.value.temperatureFour[
          realtimeChartData.value.temperatureFour.length - 1
        ][1]
      const cureTime = parseInt(parameters.time) * 60
      const diff = cureTime - lastTime
      if (lastTime < cureTime && diff <= 3) {
        console.log('begin compensation : ', diff)
        // 补偿数据
        const powerArr: number[][] = []
        const temperatureOneArr: number[][] = []
        const temperatureTwoArr: number[][] = []
        const temperatureThreeArr: number[][] = []
        const temperatureFourArr: number[][] = []
        for (let index = 0; index < diff; index++) {
          powerArr.push([lastTime + 1, lastPower])
          temperatureOneArr.push([lastTime + 1, lastTemperatureOne])
          temperatureTwoArr.push([lastTime + 1, lastTemperatureTwo])
          temperatureThreeArr.push([lastTime + 1, lastTemperatureThree])
          temperatureFourArr.push([lastTime + 1, lastTemperatureFour])
        }

        realtimeChartData.value.power = [
          ...realtimeChartData.value.power,
          ...powerArr
        ]
        realtimeChartData.value.temperatureOne = [
          ...realtimeChartData.value.temperatureOne,
          ...temperatureOneArr
        ]
        realtimeChartData.value.temperatureTwo = [
          ...realtimeChartData.value.temperatureTwo,
          ...temperatureTwoArr
        ]
        realtimeChartData.value.temperatureThree = [
          ...realtimeChartData.value.temperatureThree,
          ...temperatureThreeArr
        ]
        realtimeChartData.value.temperatureFour = [
          ...realtimeChartData.value.temperatureFour,
          ...temperatureFourArr
        ]

        console.log('end compensation : ', realtimeChartData.value)
      }
    }
  }

  // 监听启动，及启动逻辑判断
  watch(
    () => state.boot,
    boot => {
      if (boot) {
        // 处于暂停状态，继续绘制
        if (state.pause) {
          startDraw()
          state.pause = false
        } else {
          // 重新开始一轮绘制
          realtimeChartRef.value.resetYLabel()
          medicalStore.setCureParams(null)
          parameters.time = parameters.countdown
          startDraw()

          realtimeChartData.value = {
            power: [[0, 0]],
            temperatureOne: [[0, parseFloat(parameters.temperatureOne)]],
            temperatureTwo: [[0, parseFloat(parameters.temperatureTwo)]],
            temperatureThree: [[0, parseFloat(parameters.temperatureThree)]],
            temperatureFour: [[0, parseFloat(parameters.temperatureFour)]]
          }
        }
      } else {
        // 暂停或者停止
        clearTimer()
        drawCount.value = 0
        waveChartData.value = []

        // 非暂停引起的中止输出
        if (!state.pause) {
          ElMessageBox.confirm('治疗完成，请进行病历档案管理！', '注意', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
            .then(() => {
              goMedical()
            })
            .catch(() => {})
        }
      }
    }
  )

  // 监听暂停，及暂停、启动逻辑判断
  watch(
    () => state.pause,
    pause => {
      if (pause) {
        state.boot = false
      }
    }
  )

  // 监听静音
  let originVolume: number | undefined
  watch(
    () => state.mute,
    mute => {
      if (mute) {
        originVolume = parameters.volume
        parameters.volume = 0
      } else {
        if (originVolume) parameters.volume = originVolume
        originVolume = undefined
      }
    }
  )

  // 传感器故障提示
  let sensorFaultHandler: MessageHandler | undefined
  watch(
    () => parameters.sensorFault,
    fault => {
      if (fault) {
        showSensorFaultMessage()

        if (panelRef.value) panelRef.value.handleStop()

        medicalStore.setSensorFault(true)
      } else {
        closeSensorFaultMessage()

        medicalStore.setSensorFault(false)
      }
    }
  )
  function showSensorFaultMessage() {
    if (sensorFaultHandler === undefined) {
      sensorFaultHandler = ElMessage({
        showClose: false,
        message: '测温传感器故障',
        type: 'warning',
        duration: 0,
        grouping: true
      })
    }
  }
  function closeSensorFaultMessage() {
    if (sensorFaultHandler) {
      sensorFaultHandler.close()
      sensorFaultHandler = undefined
    }
  }

  // 通讯故障异常提示
  let communicationFaultHandler: MessageHandler | undefined
  watch(
    () => parameters.communicationFault,
    fault => {
      if (fault) {
        showCommunicationFaultMessage()

        medicalStore.setCommunicationFault(true)
      } else {
        closeCommunicationFaultMessage()

        medicalStore.setCommunicationFault(false)
      }
    }
  )
  function showCommunicationFaultMessage() {
    if (communicationFaultHandler === undefined) {
      communicationFaultHandler = ElMessage({
        showClose: false,
        message: '测温通讯故障',
        type: 'warning',
        duration: 0,
        grouping: true
      })
    }
  }
  function closeCommunicationFaultMessage() {
    if (communicationFaultHandler) {
      communicationFaultHandler.close()
      communicationFaultHandler = undefined
    }
  }

  // 锁屏给提示
  let lockscreenNotifyId: number | undefined
  let lockscreenHandler: MessageHandler | undefined
  watch(
    () => state.lockscreen,
    lockscreen => {
      if (lockscreen) {
        showLockScreenMessage()
      } else {
        closeLockscreenMessage()
      }
    }
  )
  function showLockScreenMessage() {
    // if (lockscreenHandler === undefined) {
    //   lockscreenHandler = ElMessage({
    //     showClose: false,
    //     message: '已锁屏！请由专业人员进行操作！',
    //     type: 'warning',
    //     duration: 0,
    //     offset: 800,
    //     grouping: true
    //   })
    // }

    if (lockscreenNotifyId === undefined) {
      const { id } = useBottomWarnNotify('已锁屏！请由专业人员进行操作！')
      lockscreenNotifyId = id
    }
  }
  function closeLockscreenMessage() {
    // if (lockscreenHandler) {
    //   lockscreenHandler.close()
    //   lockscreenHandler = undefined
    // }

    console.log('lockscreenNotifyId : ', lockscreenNotifyId)
    if (lockscreenNotifyId) {
      closeNotify(lockscreenNotifyId)
      lockscreenNotifyId = undefined
    }
  }

  // 切换页面方法
  function goMedical() {
    if (state.lockscreen || state.boot) return
    let params: ICureParams = {}

    const existParams: ICureParams | null = medicalStore.getCureParams
    console.log(`existParams : `, existParams)
    if (existParams) {
      params = existParams
    } else {
      if (parameters.radiatorSelected) {
        params.radiator = radiatorNameMap[parameters.radiator]
      }

      params.realtimeChartData = realtimeChartData.value
    }

    params.time = parameters.time
    // params.prescriptionId = parameters.prescriptionId
    // params.sensorFault = parameters.sensorFault
    // params.communicationFault = parameters.communicationFault

    medicalStore.setSensorFault(!!parameters.sensorFault)
    medicalStore.setCommunicationFault(!!parameters.communicationFault)
    medicalStore.setCureParams(params)
    medicalStore.setPdfContent(null)
    medicalStore.setPrintParams(null)
    console.log('getCurrentPatientId : ', medicalStore.getCurrentPatientId)

    window.electronAPI.serialport.dispatch(`WavePage.Exit`)
    router.replace({
      path: `/patient-case/management`,
      query: { patientId: medicalStore.getCurrentPatientId }
    })
  }

  // 页面退出方法
  function handleLogout() {
    // 若锁屏，禁止退出
    if (state.lockscreen) {
      window.electronAPI.serialport.dispatch(`WavePage.Exit`)
      return
    }

    if (state.pause) {
      ElMessageBox.confirm('是否保存病历信息？', '提示', {
        confirmButtonText: '是',
        cancelButtonText: '否',
        type: 'warning'
      })
        .then(() => {
          goMedical()
        })
        .catch(() => {
          performBack()
        })
    } else {
      performBack()
    }
  }

  function performBack() {
    medicalStore.setCureParams(null)
    window.electronAPI.serialport.dispatch(`WavePage.Exit`)
    setTimeout(() => {
      router.replace('/action/choose')
    }, 500)
  }

  watch(router.currentRoute, route => {
    console.log('route query : ', route.query)
    // 处方选择跳转逻辑处理
    if (route.query.time) {
      parameters.time = route.query.time as string
    } else {
      parameters.time = ''
    }

    if (route.query.id) {
      parameters.prescriptionId = Number(route.query.id)
    } else {
      parameters.prescriptionId = 0
    }
  })

  return {
    loading,
    timer,
    drawCount,
    waveChartData,
    realtimePowerTimer,
    realtimePowerData,
    parameters,
    state,
    sensorFaultHandler,
    communicationFaultHandler,
    panelRef,
    lockscreenHandler,
    realtimeChartRef,
    realtimeChartData,
    drawRealtimePower,
    handleLogout,
    goMedical,
    touchScreenInfo,
    touchScreenVersionNameArr
  }
}
