import { init } from '@cornerstonejs/dicom-image-loader'
import { setOptions } from '@cornerstonejs/dicom-image-loader/dist/esm/imageLoader/internal'

export default function initCornerstoneDICOMImageLoader() {
  const config = {
    maxWebWorkers: navigator.hardwareConcurrency || 1,
    startWebWorkersOnDemand: true,
    taskConfiguration: {
      decodeTask: {
        initializeCodecsOnStartup: false,
        strict: false
      }
    },
    decodeConfig: {
      convertFloatPixelDataToInt: false,
      use: false
    }
  }

  setOptions(config)
  init()
}
