import type { RouteRecordRaw } from 'vue-router'
import Layout from '@/layouts/AboveTwoLevel.vue'

export const LoadingRoute: RouteRecordRaw = {
  path: '/',
  name: 'LoadingPage',
  component: () => import('@/views/home/<USER>'),
  meta: {
    title: '主页'
  }
}

export const LoginRoute: RouteRecordRaw = {
  path: '/login',
  name: 'Login',
  component: Layout,
  meta: {
    title: '登录'
  },
  children: [
    {
      path: '',
      name: 'UserLogin',
      component: () => import('@/views/login/index.vue'),
      meta: {
        title: '登录'
      }
    },
    {
      path: 'super-admin',
      name: 'SuperAdminLogin',
      component: () => import('@/views/login/super-admin.vue'),
      meta: {
        title: '登录'
      }
    }
  ]
}

export const HomeRoute: RouteRecordRaw = {
  path: '/home',
  name: 'Home',
  component: () => import('@/views/home/<USER>'),
  meta: {
    title: '主页'
  }
}

export const IntroRoutes: RouteRecordRaw = {
  path: '/introduction',
  name: 'Introduction',
  component: Layout,
  meta: {
    title: '操作'
  },
  children: [
    {
      path: 'menu',
      name: 'IntroductionMenu',
      component: () => import('@/views/introduction/menu/index.vue'),
      meta: {
        title: '简介菜单'
      }
    },
    {
      path: 'company',
      name: 'IntroductionCompany',
      component: () => import('@/views/introduction/company/index.vue'),
      meta: {
        title: '企业简介'
      }
    },
    {
      path: 'principle',
      name: 'IntroductionPrinciple',
      component: () => import('@/views/introduction/principle/index.vue'),
      meta: {
        title: '工作原理'
      }
    },
    {
      path: 'scope',
      name: 'IntroductionScope',
      component: () => import('@/views/introduction/scope/index.vue'),
      meta: {
        title: '适用范围'
      }
    },
    {
      path: 'contraindication',
      name: 'IntroductionContraindication',
      component: () =>
        import('@/views/introduction/contraindication/index.vue'),
      meta: {
        title: '禁忌症'
      }
    },
    {
      path: 'attention',
      name: 'IntroductionAttention',
      component: () => import('@/views/introduction/attention/index.vue'),
      meta: {
        title: '注意事项'
      }
    },
    {
      path: 'troubleshooting',
      name: 'IntroductionTroubleshooting',
      component: () => import('@/views/introduction/troubleshooting/index.vue'),
      meta: {
        title: '故障排除'
      }
    }
  ]
}

export const ActionRoutes: RouteRecordRaw = {
  path: '/action',
  name: 'Action',
  component: Layout,
  meta: {
    title: '操作'
  },
  children: [
    {
      path: 'choose',
      name: 'ActionChoose',
      component: () => import('@/views/action/choose/index.vue'),
      meta: {
        title: '操作选择'
      }
    },
    {
      path: 'cure',
      name: 'ActionCure',
      component: Layout,
      children: [
        {
          path: '',
          name: 'ActionCureChoose',
          component: () => import('@/views/action/cure/index.vue'),
          meta: {
            title: '波形选择'
          }
        },
        // {
        //   path: 'pulse',
        //   name: 'ActionCurePulse',
        //   component: () => import('@/views/action/cure/pulse.vue'),
        //   meta: {
        //     title: '脉冲波治疗'
        //   }
        // },
        // {
        //   path: 'triangle',
        //   name: 'ActionCureTriangle',
        //   component: () => import('@/views/action/cure/triangle.vue'),
        //   meta: {
        //     title: '三角波治疗'
        //   }
        // },
        // {
        //   path: 'sine',
        //   name: 'ActionCureSine',
        //   component: () => import('@/views/action/cure/sine.vue'),
        //   meta: {
        //     title: '正弦波治疗'
        //   }
        // },
        {
          path: 'continuation',
          name: 'ActionCureContinuation',
          component: () => import('@/views/action/cure/continuation.vue'),
          meta: {
            title: '连续波治疗'
          }
        }
        // {
        //   path: 'continuation-realtime-power',
        //   name: 'ActionCureContinuationRealtimePower',
        //   component: () =>
        //     import('@/views/action/cure/continuation-realtime-power.vue'),
        //   meta: {
        //     title: '连续波治疗显示实时功率'
        //   }
        // }
      ]
    },
    {
      path: 'calibration',
      name: 'ActionCalibration',
      component: Layout,
      meta: {
        title: '校准'
      },
      children: [
        {
          path: '',
          name: 'ActionCalibrationChoose',
          component: () => import('@/views/action/calibration/index.vue'),
          meta: {
            title: '温度校准选择'
          }
        },
        {
          path: 'temperature-one',
          name: 'ActionCalibrationTemperatureOne',
          component: () =>
            import('@/views/action/calibration/temperature-one.vue'),
          meta: {
            title: 'T1温度校准'
          }
        },
        {
          path: 'temperature-two',
          name: 'ActionCalibrationTemperatureTwo',
          component: () =>
            import('@/views/action/calibration/temperature-two.vue'),
          meta: {
            title: 'T2温度校准'
          }
        },
        {
          path: 'temperature-three',
          name: 'ActionCalibrationTemperatureThree',
          component: () =>
            import('@/views/action/calibration/temperature-three.vue'),
          meta: {
            title: 'T3温度校准'
          }
        },
        {
          path: 'temperature-four',
          name: 'ActionCalibrationTemperatureFour',
          component: () =>
            import('@/views/action/calibration/temperature-four.vue'),
          meta: {
            title: 'T4温度校准'
          }
        },
        {
          path: 'power',
          name: 'ActionCalibrationPower',
          component: () => import('@/views/action/calibration/power.vue'),
          meta: {
            title: '功率校准'
          }
        }
      ]
    }
  ]
}

export const PatientCaseRoutes: RouteRecordRaw = {
  path: '/patient-case',
  name: 'PatientCase',
  component: Layout,
  meta: {
    title: '病历管理'
  },
  children: [
    {
      path: 'management',
      name: 'PatientCaseManagement',
      component: () => import('@/views/patient-case/index.vue'),
      meta: {
        title: '病人信息管理'
      }
    }
  ]
}

export const MicrowaveRoutes: RouteRecordRaw = {
  path: '/microwave',
  name: 'Microwave',
  component: Layout,
  meta: {
    title: '微波治疗控制程序'
  },
  children: [
    {
      path: 'medical-record-print',
      name: 'MicrowaveCurePrint',
      component: () => import('@/views/microwave/main/print.vue'),
      meta: {
        title: '微波治疗控制程序病历打印预览界面'
      }
    }
  ]
}

export const UserRoutes: RouteRecordRaw = {
  path: '/user',
  name: 'UserManagement',
  component: Layout,
  meta: {
    title: '用户管理'
  },
  children: [
    {
      path: 'management',
      name: 'UserList',
      component: () => import('@/views/user/index.vue'),
      meta: {
        title: '用户信息管理'
      }
    }
  ]
}

export const basicRoutes = [
  LoadingRoute,
  LoginRoute,
  HomeRoute,
  IntroRoutes,
  ActionRoutes,
  PatientCaseRoutes,
  MicrowaveRoutes,
  UserRoutes
]
