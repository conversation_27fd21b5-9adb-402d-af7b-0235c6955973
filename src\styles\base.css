@tailwind base;
@tailwind components;
@tailwind utilities;

html,
body {
  font-family: 黑体, Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB,
    Microsoft YaHei, SimSun, sans-serif;
}

#app {
  /* @apply w-screen h-screen bg-no-repeat bg-cover bg-center text-xl;
  background-image: url(@/assets/img/app-bg.jpg); */
  @apply w-screen h-screen text-xl;
}

.loading-div {
  /* @apply bg-no-repeat bg-cover bg-center text-xl;
  background-image: url(@/assets/img/app-bg.jpg); */
}

@layer components {
  .page-wrapper {
    @apply w-full h-full flex justify-center items-center text-gray-900 bg-white;
    position: relative;
  }

  .content-wrapper {
    width: 1450px;
    height: 850px;
    /* background-image: url(@/assets/img/content-bg.png); */
    background-size: 100% 100%;
    @apply bg-no-repeat;
  }

  .flex-center {
    @apply flex justify-center items-center;
  }

  .col-flex {
    @apply flex flex-col;
  }

  .bg-nova {
    background-color: #409eff;
  }

  .bg-exit {
    background-color: #e6a23c;
  }

  .choose-item {
    @apply btn btn-lg btn-wide btn-primary !min-h-12 h-12 text-2xl bg-nova text-white rounded;
    min-height: 48px;
    height: 48px;
  }

  .menu-item {
    @apply btn btn-lg btn-wide btn-primary text-2xl text-white bg-nova rounded;
    min-height: 48px !important;
    height: 48px !important;
  }

  .bg-power-selected {
    background-color: #f56c6c;
  }

  .bg-power-label {
    background-color: #909399;
  }

  .temperature-calibration-button {
    @apply btn btn-sm btn-primary mx-6 rounded text-white font-normal;
  }

  .inner-shadow {
    box-shadow: 1px 1px 2px rgb(0 0 0 / 0.3) inset;
  }
}

/* 隐藏滚动条 */
.scrollbar-hidden::-webkit-scrollbar {
  width: 0;
  height: 0;
  display: none;
}

.el-form-item__label {
  user-select: none;
}

body {
  user-select: none;
}

/* @media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }
  a:hover {
    color: #747bff;
  }
  button {
    background-color: #f9f9f9;
  }
  code {
    background-color: #f9f9f9;
  }
} */

.el-message .el-message__badge {
  display: none;
}

.nova-bottom-toast.el-message {
  top: initial;
  display: flex;
  align-items: center;
}

.home-logo,
.wrapper-logo {
  -webkit-user-drag: none;
}
