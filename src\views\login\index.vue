<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  init as csInit,
  cache,
  Enums as csEnums,
  RenderingEngine,
  getRenderingEngine,
  setVolumesForViewports,
  volumeLoader,
  utilities as csUtilities
} from "@cornerstonejs/core";
import {
  init as cstInit,
  addTool,
  Enums as cstEnums,
  PanTool,
  StackScrollTool,
  ToolGroupManager,
  ZoomTool
} from "@cornerstonejs/tools";
import cornerstoneDICOMImageLoader from "@cornerstonejs/dicom-image-loader";
import dicomParser from "dicom-parser";

// Create refs
const elementRef = ref<HTMLDivElement | null>(null)
const fileImageIds = ref<string[]>([]);
const isInitialized = ref(false);
const loading = ref(false);

const renderingEngineId = "myRenderingEngine";
const viewportId1 = "CT_AXIAL";
const viewportId2 = "CT_SAGITTAL";
const viewportId3 = "CT_CORONAL";
const volumeId = "CT_VOLUME";
const toolGroupId = "CT_TOOLGROUP";

// 初始化cornerstone库
async function initCornerstone() {
  if (isInitialized.value) {
    console.log("Cornerstone already initialized");
    return;
  }

  try {
    console.log("Starting Cornerstone initialization...");

    // 1. 初始化cornerstone核心库
    console.log("Initializing Cornerstone core...");
    await csInit();

    // 2. 初始化cornerstone工具库
    console.log("Initializing Cornerstone tools...");
    await cstInit();

    // 3. 配置cornerstone DICOM加载器
    console.log("Configuring DICOM image loader...");
    cornerstoneDICOMImageLoader.init({
      maxWebWorkers: navigator.hardwareConcurrency || 1,
      decodeConfig: {
        convertFloatPixelDataToInt: false,
      },
    });

    // 4. 注册工具
    console.log("Registering tools...");
    addTool(PanTool);
    addTool(ZoomTool);
    addTool(StackScrollTool);

    // 5. 创建工具组
    console.log("Setting up tool group...");
    const toolGroup = ToolGroupManager.createToolGroup(toolGroupId);
    if (toolGroup) {
      // 添加工具到工具组
      toolGroup.addTool(PanTool.toolName);
      toolGroup.addTool(ZoomTool.toolName);
      toolGroup.addTool(StackScrollTool.toolName);

      // 设置工具激活状态
      toolGroup.setToolActive(PanTool.toolName, {
        bindings: [{
          mouseButton: cstEnums.MouseBindings.Primary
        }]
      });
      toolGroup.setToolActive(ZoomTool.toolName, {
        bindings: [{
          mouseButton: cstEnums.MouseBindings.Secondary
        }]
      });
      toolGroup.setToolActive(StackScrollTool.toolName, {
        bindings: [{
          mouseButton: cstEnums.MouseBindings.Wheel
        }]
      });
    }

    // 6. 创建渲染引擎
    console.log("Creating rendering engine...");
    new RenderingEngine(renderingEngineId);

    isInitialized.value = true;
    console.log("Cornerstone initialized successfully");
  } catch (error) {
    console.error("Cornerstone initialization failed:", error);
    isInitialized.value = false;
    ElMessage.error("初始化失败，请刷新页面重试");
    throw error; // 重新抛出错误以便调用者处理
  }
}

function handleChange(evt: Event) {
  // 阻止事件冒泡
  evt.stopPropagation();
  evt.preventDefault();

  if (!isInitialized.value) {
    ElMessage.warning("系统正在初始化，请稍后再试");
    return;
  }

  loading.value = true;

  // 更新文件前清除之前的缓存
  cache.purgeCache();

  const files: FileList = (evt.target as HTMLInputElement).files!;

  if (files.length === 0) {
    loading.value = false;
    return;
  }

  try {
    const imageIds: string[] = [];
    console.log('处理DICOM文件...');

    Array.from(files).forEach(file => {
      const imageId = cornerstoneDICOMImageLoader.wadouri.fileManager.add(file);
      imageIds.push(imageId);
    });

    fileImageIds.value = imageIds;
    loadAndViewImage(imageIds);
  } catch (error) {
    console.error("处理文件失败:", error);
    ElMessage.error("处理文件失败，请确保选择的是有效的DICOM文件");
    loading.value = false;
  }
}

async function loadAndViewImage(imageIds: string[]) {
  try {
    if (imageIds.length === 0) {
      ElMessage.warning("未选择任何文件");
      loading.value = false;
      return;
    }

    if (imageIds.length < 5) {
      ElMessage.warning("请至少选择5张DICOM图片进行MPR展示");
      loading.value = false;
      return;
    }

    console.log('开始加载元数据...');
    await prefetchMetadataInformation(imageIds);
    console.log('元数据加载完成，开始渲染...');
    await renderVolume(imageIds);
    loading.value = false;
  } catch (error) {
    console.error("加载图像失败:", error);
    ElMessage.error("加载图像失败，请确保选择的是有效的DICOM文件");
    loading.value = false;
  }
}

async function prefetchMetadataInformation(imageIdsToPrefetch: string[]) {
  const loadPromises = imageIdsToPrefetch.map(imageId => {
    return cornerstoneDICOMImageLoader.wadouri.loadImage(imageId).promise;
  });

  try {
    await Promise.all(loadPromises);
  } catch (error) {
    console.error("加载元数据失败:", error);
    throw new Error("加载元数据失败");
  }
}

async function renderVolume(imageIds: string[]) {
  // 在缓存中删除上一次加载的影像
  if (cache.getVolume(volumeId)) {
    cache.removeVolumeLoadObject(volumeId);
  }

  const renderingEngine = new RenderingEngine(renderingEngineId);

  // 确保DOM元素存在
  const element1 = document.getElementById("element1") as HTMLDivElement;
  const element2 = document.getElementById("element2") as HTMLDivElement;
  const element3 = document.getElementById("element3") as HTMLDivElement;

  if (!element1 || !element2 || !element3) {
    throw new Error("找不到渲染容器元素");
  }

  // 设置视口
  const viewportInputArray = [
    {
      viewportId: viewportId1,
      type: csEnums.ViewportType.ORTHOGRAPHIC,
      element: element1,
      defaultOptions: {
        orientation: csEnums.OrientationAxis.AXIAL
      }
    },
    {
      viewportId: viewportId2,
      type: csEnums.ViewportType.ORTHOGRAPHIC,
      element: element2,
      defaultOptions: {
        orientation: csEnums.OrientationAxis.SAGITTAL
      }
    },
    {
      viewportId: viewportId3,
      type: csEnums.ViewportType.ORTHOGRAPHIC,
      element: element3,
      defaultOptions: {
        orientation: csEnums.OrientationAxis.CORONAL
      }
    }
  ];

  renderingEngine.setViewports(viewportInputArray);

  // 将视口添加到工具组
  const toolGroup = ToolGroupManager.getToolGroup(toolGroupId);
  if (toolGroup) {
    toolGroup.addViewport(viewportId1, renderingEngineId);
    toolGroup.addViewport(viewportId2, renderingEngineId);
    toolGroup.addViewport(viewportId3, renderingEngineId);
  }

  try {
    // 创建并加载体积数据
    const volume = await volumeLoader.createAndCacheVolume(volumeId, {
      imageIds
    });

    await volume.load();

    // 设置视口的体积数据
    await setVolumesForViewports(
      renderingEngine,
      [{ volumeId }],
      [viewportId1, viewportId2, viewportId3]
    );

    // 渲染
    renderingEngine.render();

    console.log('渲染完成');
    ElMessage.success("DICOM图像加载成功");
  } catch (error) {
    console.error("渲染体积数据失败:", error);
    throw new Error("渲染体积数据失败");
  }
}

// 组件挂载时初始化cornerstone
onMounted(async () => {
  try {
    await initCornerstone();
  } catch (e) {
    console.log('initCornerstone error : ', e)
  }
});
</script>

<template>
  <div class="dicom-viewer-container">
    <div class="control-panel">
      <input type="file" multiple accept=".dcm" @change="handleChange" :disabled="loading" />
      <div v-if="loading" class="loading-indicator">加载中...</div>
    </div>

    <div class="viewport-container">
      <div id="element1" class="cornerstone-viewport" />
      <div id="element2" class="cornerstone-viewport" />
      <div id="element3" class="cornerstone-viewport" />
    </div>
  </div>
</template>

<style scoped>
.dicom-viewer-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100vh;
  padding: 20px;
  box-sizing: border-box;
}

.control-panel {
  margin-bottom: 20px;
}

.loading-indicator {
  margin-top: 10px;
  color: #409EFF;
  font-weight: bold;
}

.viewport-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.cornerstone-viewport {
  width: 300px;
  height: 300px;
  border: 2px solid #96CDF2;
  border-radius: 10px;
  background-color: #000;
}
</style>
