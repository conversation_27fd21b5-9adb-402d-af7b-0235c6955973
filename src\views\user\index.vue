<template>
  <div class="user-management">
    <h1 class="w-full flex justify-between">
      <span>用户管理</span>
      <el-button :icon="ArrowLeft" size="small" @click="goBack">返回</el-button>
    </h1>

    <!-- 查询条件区域 -->
    <el-form :inline="true" :model="queryCriteria" class="query-form">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item label="用户名:" class="w-full">
            <el-input v-model="queryCriteria.keyword" placeholder="请输入" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="角色:" class="w-full">
            <el-select v-model="queryCriteria.role" placeholder="请选择" clearable class="w-full">
              <el-option label="管理员" :value="UserRole.ADMIN" />
              <el-option label="用户" :value="UserRole.USER" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item class="w-full">
            <div class="w-full flex justify-end">
              <el-button type="primary" @click="handleQuery">查询</el-button>
              <el-button @click="resetQuery">重置</el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <!-- 新增用户按钮 -->
    <el-button type="primary" :icon="Plus" @click="showAddUserDialog" style="margin-bottom: 20px;">新增用户</el-button>

    <!-- 用户列表 -->
    <el-table :data="users" empty-text="暂无数据" :height="720" style="width: 100%">
      <el-table-column type="index" label="序号" header-align="center" align="center" width="120" />
      <el-table-column prop="loginName" label="用户名" header-align="center" align="center" />
      <el-table-column prop="role" label="角色" header-align="center" align="center">
        <template #default="{ row }">
          <span v-if="row.role === UserRole.SUPER">厂家管理员</span>
          <span v-else-if="row.role === UserRole.ADMIN">管理员</span>
          <span v-else>用户</span>
        </template>
      </el-table-column>
      <el-table-column prop="latestLoginTime" label="最后登录时间" header-align="center" align="center">
        <template #default="{ row }">
          <span>{{ formatDate(row.latestLoginTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="300" header-align="center" align="center">
        <template #default="{ row }">
          <el-button v-if="row.role === UserRole.USER" size="small" type="success"
            @click="editUser(row)">修改用户名</el-button>
          <el-button size="small" type="warning" @click="showChangePasswordDialog(row)">重置密码</el-button>
          <el-button v-if="row.role === UserRole.USER" size="small" type="danger"
            @click="deleteUser(row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 新增/编辑用户表单 -->
    <el-dialog v-model="dialogVisible" :title="isEditing ? '编辑用户' : '新增用户'">
      <el-form ref="userFormRef" :model="userForm" label-width="120px" :rules="rules">
        <el-form-item label="用户名" prop="loginName">
          <el-input v-model="userForm.loginName" :disabled="userForm.role === UserRole.ADMIN" placeholder="请填写"
            clearable />
        </el-form-item>
        <el-form-item v-if="!isEditing" label="密码" prop="password">
          <el-input v-model="userForm.password" type="password" placeholder="请填写" show-password clearable />
        </el-form-item>
        <el-form-item v-if="!isEditing" label="确认密码" prop="confirmPassword">
          <el-input v-model="userForm.confirmPassword" type="password" placeholder="请填写" show-password clearable />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveUser">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 修改密码表单 -->
    <el-dialog v-model="resetPasswordDialogVisible" title="重置密码" :before-close="resetChangePasswordForm">
      <el-form ref="changePasswordFormRef" :model="changePasswordForm" label-width="120px" :rules="changePasswordRules">
        <el-form-item label="新密码" prop="password">
          <el-input v-model="changePasswordForm.password" type="password" placeholder="请填写" show-password clearable />
        </el-form-item>
        <el-form-item label="确认新密码" prop="confirmPassword">
          <el-input v-model="changePasswordForm.confirmPassword" type="password" placeholder="请填写" show-password
            clearable />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="resetPasswordDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="resetPassword">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, toRaw, onMounted } from 'vue'
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus'
import { Plus, ArrowLeft } from '@element-plus/icons-vue'
import { type User, UserRole } from '@/types/entity';
import { UserQueryCriteria } from '@/types/entity'
import { formatDate } from '@/utils'
import { useUserStore } from '@/store/modules/user';

// 当前用户名
const userStore = useUserStore()

// 返回上一级页面
const router = useRouter()
function goBack() {
  router.back()
}

// 用户列表
const users = ref<User[]>([])

// 查询表单
const queryCriteria = reactive<UserQueryCriteria>({
  keyword: undefined,
  role: undefined
})

// 处理查询
const handleQuery = async () => {
  users.value = await window.electronAPI.sqlite.queryUsers({ ...toRaw(queryCriteria), isSuper: userStore.getUserRole === UserRole.SUPER })
  // ElMessage.success('查询完成')
  console.log('user list : ', users.value)
}

// 重置查询条件
const resetQuery = () => {
  queryCriteria.keyword = undefined
  queryCriteria.role = undefined
  // ElMessage.info('查询条件已重置')
}

// 用户表单
const userFormRef = ref()
const userForm = reactive<Partial<User> & { confirmPassword: string | undefined }>({
  id: undefined,
  loginName: '',
  role: undefined,
  password: '',
  confirmPassword: '',
})

// 用户表单校验
const rules = {
  loginName: [
    { required: true, message: '请输入登录名', trigger: 'blur' },
    { min: 3, max: 20, message: '登录名长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  // role: [
  //   { required: true, message: '请选择角色', trigger: 'change' }
  // ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    {
      validator: (_: any, value: string, callback: any) => {
        // 长度校验
        if (value.length < 6 || value.length > 16) {
          return callback(new Error("密码长度必须在6到16位之间"))
        }
        const reg = /^[a-zA-Z0-9]+$/
        if (reg.test(value)) {
          callback()
        } else {
          callback(new Error("密码只能包含大写字母、小写字母或数字"))
        }
      },
      trigger: 'blur'
    }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    {
      validator: (_: any, value: string, callback: any) => {
        if (value === userForm.password) {
          callback()
        } else {
          callback(new Error('两次输入的密码不一致'))
        }
      }, trigger: 'blur'
    }
  ]
}

// 是否可编辑

// 控制对话框显示
const dialogVisible = ref(false)
const isEditing = ref(false)

// 显示新增用户对话框
const showAddUserDialog = async () => {
  const users = await window.electronAPI.sqlite.queryUsers({ role: UserRole.USER })
  if (users.length >= 50) {
    ElMessage.warning('账号数量已达上限，请联系管理员')
    return
  }
  isEditing.value = false
  userFormRef.value?.resetFields()
  userForm.id = undefined
  userForm.loginName = ''
  userForm.password = ''
  userForm.confirmPassword = ''
  userForm.role = undefined
  dialogVisible.value = true
}

// 编辑用户
const editUser = (user: User) => {
  isEditing.value = true
  userFormRef.value?.resetFields()
  Object.assign(userForm, user)
  dialogVisible.value = true
}

// 保存用户（新增或更新）
const saveUser = async () => {
  try {
    const valid = await userFormRef.value?.validate()
    if (!valid) {
      return
    }

    if (isEditing.value) {
      const res = await window.electronAPI.sqlite.updateUser(toRaw(userForm) as any)

      if (res.success) {
        ElMessage.success('用户信息更新成功')
        dialogVisible.value = false
        // 刷新列表数据
        handleQuery()
      } else {
        ElMessage.error(res.message || '用户信息更新失败')
      }
    } else {
      const res = await window.electronAPI.sqlite.createUser(toRaw(userForm) as any)

      if (res.success) {
        ElMessage.success('用户新增成功')
        dialogVisible.value = false
        // 刷新列表数据
        handleQuery()
      } else {
        ElMessage.warning(res.message || '用户名已存在，请重新输入')
      }
    }
  } catch (err) {
    console.log('save user error : ', err)
  }
}

// 删除用户
const deleteUser = async (id: number) => {
  ElMessageBox.confirm(
    '确定要删除这个用户吗？此操作不可逆。',
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(async () => {
    await window.electronAPI.sqlite.deleteUser(id)
    ElMessage.success('用户删除成功')
    // 刷新列表数据
    handleQuery()
  })
    .catch(() => {
      // ElMessage.info('已取消删除')
    })
}

// 修改密码弹框
const resetPasswordDialogVisible = ref(false)

// 修改密码表单
// 用户表单
const changePasswordFormRef = ref()
const changePasswordForm = reactive<Partial<User> & { confirmPassword: string | undefined }>({
  id: undefined,
  loginName: '',
  role: undefined,
  password: '',
  confirmPassword: '',
})

const resetChangePasswordForm = (done: () => void) => {
  changePasswordForm.id = undefined
  changePasswordForm.loginName = ''
  changePasswordForm.role = undefined
  changePasswordForm.password = ''
  changePasswordForm.confirmPassword = ''

  done()
}

// 用户表单校验
const changePasswordRules = {
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    {
      validator: (_: any, value: string, callback: any) => {
        // 长度校验
        if (value.length < 6 || value.length > 16) {
          return callback(new Error("密码长度必须在6到16位之间"))
        }
        const reg = /^[a-zA-Z0-9]+$/
        if (reg.test(value)) {
          callback()
        } else {
          callback(new Error("密码只能包含大写字母、小写字母或数字"))
        }
      },
      trigger: 'blur'
    }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    {
      validator: (_: any, value: string, callback: any) => {
        if (value === changePasswordForm.password) {
          callback()
        } else {
          callback(new Error('两次输入的密码不一致'))
        }
      }, trigger: 'blur'
    }
  ]
}

// 修改密码
const showChangePasswordDialog = (user: User) => {
  changePasswordForm.id = user.id
  changePasswordForm.loginName = user.loginName
  changePasswordForm.role = user.role
  changePasswordForm.password = ''
  changePasswordForm.confirmPassword = ''
  resetPasswordDialogVisible.value = true
}

// 修改密码
const resetPassword = async () => {
  const valid = await changePasswordFormRef.value?.validate()
  if (!valid) {
    return
  }

  const res = await window.electronAPI.sqlite.resetUserPassword({ userId: changePasswordForm.id!, newPassword: changePasswordForm.password! })

  ElMessage.success(res.message || '重置密码成功')

  resetPasswordDialogVisible.value = false
}

onMounted(() => {
  handleQuery()
})
</script>

<style scoped>
.user-management {
  margin: 0 auto;
  padding: 20px 50px;
}

h1 {
  margin-bottom: 20px;
}

.query-form {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.el-table {
  margin-bottom: 20px;
}
</style>
