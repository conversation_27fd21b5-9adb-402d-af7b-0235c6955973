<script lang="ts" setup>
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
    required: true
  },
  type: {
    type: String,
    default: 'success'
  },
  tip: {
    type: String,
    default: ''
  }
})
</script>

<template>
  <div v-if="props.visible" class="absolute top-4 w-80 py-3 text-xl text-center rounded-xl left-1/2 -translate-x-1/2 z-99"
    :class="[props.type === 'success' ? 'bg-success' : 'bg-warning']">
    {{
      props.tip }}
  </div>
</template>

<script lang="ts">
export default {
  name: 'Toast'
}
</script>
