<script setup lang="ts">
const props = defineProps({
  title: {
    type: String
  },
  visible: {
    type: Boolean,
    default: false
  },
  width: {
    type: Number,
    default: 600
  },
  wrapClass: {
    type: String
  }
})
</script>

<template>
  <Teleport to="body">
    <Transition name="modal-fade">
      <div v-if="visible" class="fixed left-0 top-0 right-0 bottom-0 bg-black/30 z-50">
        <div class="p-4 rounded-lg absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2" :class="[props.wrapClass]"
          :style="{ width: `${props.width}px` }">
          <slot v-if="props.title" name="title">
            <div class="flex pl-4 py-2 border-b border-gray-200">{{ props.title }}</div>
          </slot>
          <slot></slot>
          <slot name="footer"></slot>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<style>
.modal-fade-enter-active {
  animation: modal-fade-in 0.3s;
}

.modal-fade-enter-active .kr-overlay-modal {
  animation: modal-overlay-fade-in 0.3s;
}

.modal-fade-leave-active {
  animation: modal-fade-out 0.3s;
}

.modal-fade-leave-active .kr-overlay-modal {
  animation: modal-overlay-fade-out 0.3s;
}

@keyframes modal-fade-in {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

@keyframes modal-fade-out {
  0% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
}

@keyframes modal-overlay-fade-in {
  0% {
    transform: translate3d(0, -20px, 0);
    opacity: 0;
  }

  100% {
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
}

@keyframes modal-overlay-fade-out {
  0% {
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }

  100% {
    transform: translate3d(0, -20px, 0);
    opacity: 0;
  }
}
</style>
