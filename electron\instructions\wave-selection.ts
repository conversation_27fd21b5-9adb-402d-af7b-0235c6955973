export const WaveformSelection = {
  Pulse: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x00, 0x01, 0x00, 0x20],
  Triangle: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x00, 0x01, 0x00, 0x21],
  Sine: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x00, 0x01, 0x00, 0x22],
  Continuation: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x00, 0x01, 0x00, 0x23],
  Exit: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x00, 0x01, 0x00, 0x24]
}

export type WaveformSelectionMethods =
  | 'WaveformSelection.Pulse'
  | 'WaveformSelection.Triangle'
  | 'WaveformSelection.Sine'
  | 'WaveformSelection.Continuation'
  | 'WaveformSelection.Exit'
