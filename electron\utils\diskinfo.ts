import { exec } from 'node:child_process'
import os from 'node:os'

export function getDrives(callback: any): void {
  const platform = os.platform()

  if (platform === 'win32') {
    exec(
      'wmic logicaldisk get Caption,FreeSpace,Size,VolumeSerialNumber,Description  /format:list',
      function (err, stdout, stderr) {
        console.log('error : ', typeof err)
        if (err) callback(err, null)

        const aLines = stdout.split('\r\r\n')
        const aDrives: any[] = []
        let bNew = false
        let sCaption: number | string = ''
        let sDescription: number | string = ''
        let sFreeSpace: number | string = ''
        let sSize: number | string = ''
        let sVolume: number | string = ''

        for (var i = 0; i < aLines.length; i++) {
          if (aLines[i] != '') {
            var aTokens = aLines[i].split('=')
            switch (aTokens[0]) {
              case 'Caption':
                sCaption = aTokens[1]
                bNew = true
                break
              case 'Description':
                sDescription = aTokens[1]
                break
              case 'FreeSpace':
                sFreeSpace = aTokens[1]
                break
              case 'Size':
                sSize = aTokens[1]
                break
              case 'VolumeSerialNumber':
                sVolume = aTokens[1]
                break
            }
          } else {
            // Empty line
            // If we get an empty line and bNew is true then we have retrieved
            // all information for one drive, add to array and reset variables
            if (bNew) {
              sSize = parseFloat(sSize)
              if (isNaN(sSize)) {
                sSize = 0
              }
              sFreeSpace = parseFloat(sFreeSpace)
              if (isNaN(sFreeSpace)) {
                sFreeSpace = 0
              }

              let sUsed = sSize - sFreeSpace
              let sPercent = '0%'
              if (sSize > 0) {
                sPercent = Math.round((sUsed / sSize) * 100) + '%'
              }
              aDrives[aDrives.length] = {
                filesystem: sDescription,
                blocks: sSize,
                used: sUsed,
                available: sFreeSpace,
                capacity: sPercent,
                mounted: sCaption
              }
              bNew = false
              sCaption = ''
              sDescription = ''
              sFreeSpace = ''
              sSize = ''
              sVolume = ''
            }
          }
        }

        if (callback != null) {
          callback(null, aDrives)
        }
      }
    )
  }
}
