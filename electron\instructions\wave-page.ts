export const WavePage = {
  PowerUp: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x50, 0x01, 0x00, 0x05],
  PowerDown: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x51, 0x01, 0x00, 0x00],
  PeriodUp: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x54, 0x01, 0x00, 0x0a],
  PeriodDown: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x55, 0x01, 0x00, 0x01],
  VolumeUp: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x58, 0x01, 0x00, 0xef],
  VolumeDown: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x59, 0x01, 0x00, 0xe0],
  TimeUp: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x52, 0x01, 0x05, 0xa0],
  TimeDown: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x53, 0x01, 0x00, 0x3c],
  DutyRatioUp: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x56, 0x01, 0x00, 0x5a],
  DutyRatioDown: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x57, 0x01, 0x00, 0x0a],
  Mute: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x00, 0x01, 0x00, 0x19],
  LockScreen: [0x5a, 0xa5, 0x06, 0x83, 0x00, 0x00, 0x01, 0x00, 0x1a],
  Boot: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x00, 0x01, 0x00, 0x17],
  Stop: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x00, 0x01, 0x00, 0x18],
  PrescriptionSelection: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x00, 0x01, 0x00, 0x16],
  ToggleTemperatureOne: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x00, 0x01, 0x00, 0x14],
  ToggleTemperatureTwo: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x00, 0x01, 0x00, 0x15],
  ToggleRadiator: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x00, 0x01, 0x00, 0x1c],
  Exit: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x00, 0x01, 0x00, 0x1b],
  SendPageNo: [0x5a, 0xa5, 0x05, 0x81, 0x03, 0x02],
  ToggleRealtimePower: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x00, 0x01, 0x00, 0x1d],
  // 新增的波形页面按键
  // 设定温度上键
  TemperatureUp: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x5a, 0x01, 0x01, 0xa4],
  // 设定温度下键
  TemperatureDown: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x5b, 0x01, 0x01, 0xc2],
  // 暂定
  Pause: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x00, 0x01, 0x00, 0x1e],
  // 选择温度T3
  ToggleTemperatureThree: [
    0x5a, 0xa5, 0x06, 0x83, 0x10, 0x00, 0x01, 0x01, 0x00
  ],
  // 选择温度T4
  ToggleTemperatureFour: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x00, 0x01, 0x01, 0x01]
}

export type WavePageMethods =
  | 'WavePage.PowerUp'
  | 'WavePage.PowerDown'
  | 'WavePage.PeriodUp'
  | 'WavePage.PeriodDown'
  | 'WavePage.VolumeUp'
  | 'WavePage.VolumeDown'
  | 'WavePage.TimeUp'
  | 'WavePage.TimeDown'
  | 'WavePage.DutyRatioUp'
  | 'WavePage.DutyRatioDown'
  | 'WavePage.Mute'
  | 'WavePage.LockScreen'
  | 'WavePage.Boot'
  | 'WavePage.Stop'
  | 'WavePage.PrescriptionSelection'
  | 'WavePage.ToggleTemperatureOne'
  | 'WavePage.ToggleTemperatureTwo'
  | 'WavePage.ToggleRadiator'
  | 'WavePage.Exit'
  | 'WavePage.SendPageNo'
  | 'WavePage.ToggleRealtimePower'
  | 'WavePage.TemperatureUp'
  | 'WavePage.TemperatureDown'
  | 'WavePage.Pause'
  | 'WavePage.ToggleTemperatureThree'
  | 'WavePage.ToggleTemperatureFour'
