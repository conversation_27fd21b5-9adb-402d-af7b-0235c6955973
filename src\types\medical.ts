export type ICureParams = {
  waveType?: string
  time?: string
  period?: number
  dutyRatio?: number
  radiator?: string
  prescriptionId?: number
  xLabel?: string
  yLabel?: string
  realtimeChartData?: RealtimeChartData
  sensorFault?: number
  communicationFault?: number
}

export type IPrintParams = {
  id: number | null
  name: string
  sex: number | null
  age: number | null
  date: string
  sn: string
  nameWithNo: string
  detail: string
  chart: string | null
  cureParams: string | null
  hospitalName: string | null
}

export type RealtimeChartData = {
  power: number[][]
  temperatureOne: number[][]
  temperatureTwo: number[][]
  temperatureThree: number[][]
  temperatureFour: number[][]
}
