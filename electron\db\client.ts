import { ipc<PERSON>enderer as ipc } from 'electron'
import { compact, fromPairs, isFunction, map, toPairs } from 'lodash'
import createTaskWithTimeout from '../utils/taskWithTimeout'
import {
  ClientInterface,
  ServerInterface,
  UserLoginParams,
  UserQueryCriteria,
  ResetUserPasswordParams,
  UpdateUserPasswordParams,
  PatientQueryCriteria
} from './types'
import Basic from './entities/basic.entity'
import Patient from './entities/patient.entity'
import PatientCase from './entities/patient-case.entity'
import User from './entities/user.entity'

let activeJobCount = 0

const SQLITE_CHANNEL_KEY = 'sqlite-channel'

const dataInterface: ClientInterface = {
  close,

  // 软件基础信息
  getBasicInfo,
  initBasicInfo,
  updateBasicInfo,
  checkCasePath,
  // 病人信息
  queryPatients,
  createPatient,
  updatePatient,
  deletePatient,
  // 病历信息
  queryPatientCases,
  createPatientCase,
  updatePatientCase,
  deletePatientCase,
  // 反向同步病历
  // syncStoragePatients,
  // pdf
  exportPdf,
  removePdf,
  exportExcel,
  removeExcel,

  // 用户接口
  queryUsers,
  createUser,
  updateUser,
  deleteUser,
  userLogin,
  updateUserPassword,
  resetUserPassword
}

export default dataInterface

const channelsAsUnknown = fromPairs(
  compact(
    map(toPairs(dataInterface), ([name, value]: [string, unknown]) => {
      if (isFunction(value)) {
        return [name, makeChannel(name)]
      }

      return null
    })
  )
) as unknown

const channels: ServerInterface = channelsAsUnknown as ServerInterface

function makeChannel(fnName: string) {
  return async (...args: ReadonlyArray<unknown>) => {
    activeJobCount += 1

    return createTaskWithTimeout(async () => {
      try {
        // 调用
        return await ipc.invoke(SQLITE_CHANNEL_KEY, fnName, ...args)
      } finally {
        activeJobCount -= 1
        if (activeJobCount === 0) {
          // resolveShutdown?.();
        }
      }
    }, `SQL channel call (${fnName})`)()
  }
}

// Note: will need to restart the app after calling this, to set up afresh
async function close(): Promise<void> {
  await channels.close()
}

async function getBasicInfo() {
  return await channels.getBasicInfo()
}

async function initBasicInfo() {
  return await channels.initBasicInfo()
}

async function updateBasicInfo(basic: Basic) {
  return await channels.updateBasicInfo(basic)
}

async function checkCasePath() {
  return await channels.checkCasePath()
}

// 病人信息管理
async function queryPatients(params: PatientQueryCriteria) {
  return channels.queryPatients(params)
}

async function createPatient(patient: Patient) {
  return await channels.createPatient(patient)
}

async function updatePatient(patient: Patient) {
  return await channels.updatePatient(patient)
}

async function deletePatient(patientId: number) {
  return await channels.deletePatient(patientId)
}

// async function syncStoragePatients() {
//   return await channels.syncStoragePatients()
// }

function exportPdf(...args: any[]) {
  ipc.invoke('export-pdf', ...args)
}

function removePdf(...args: any[]) {
  ipc.invoke('remove-pdf', ...args)
}

function exportExcel(...args: any[]) {
  ipc.invoke('export-excel', ...args)
}

function removeExcel(...args: any[]) {
  ipc.invoke('remove-excel', ...args)
}

// 查询用户
async function queryUsers(queryCriteria: UserQueryCriteria) {
  return await channels.queryUsers(queryCriteria)
}

// 新增用户
async function createUser(user: User) {
  return await channels.createUser(user)
}

// 编辑用户
async function updateUser(user: User) {
  return await channels.updateUser(user)
}

// 逻辑删除用户
async function deleteUser(userId: number) {
  return await channels.deleteUser(userId)
}

// 用户登录
async function userLogin(params: UserLoginParams) {
  return await channels.userLogin(params)
}

// 用户修改密码
async function updateUserPassword(params: UpdateUserPasswordParams) {
  return await channels.updateUserPassword(params)
}

// 重置密码
async function resetUserPassword(params: ResetUserPasswordParams) {
  return await channels.resetUserPassword(params)
}

// 病历信息
async function queryPatientCases(patientId: number) {
  return await channels.queryPatientCases(patientId)
}

// 新增病历信息
async function createPatientCase(patientId: number, patientCase: PatientCase) {
  return await channels.createPatientCase(patientId, patientCase)
}

// 更新病历信息
async function updatePatientCase(patientId: number, patientCase: PatientCase) {
  return await channels.updatePatientCase(patientId, patientCase)
}

// 删除病历信息
async function deletePatientCase(patientId: number) {
  return await channels.deletePatientCase(patientId)
}
