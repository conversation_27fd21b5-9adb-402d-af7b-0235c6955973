import { app, <PERSON><PERSON>erWindow, shell, ipc<PERSON><PERSON>, <PERSON>u, dialog } from 'electron'
import { release } from 'node:os'
import { join } from 'node:path'
import { MainSerialPort } from '../serialport/main'
import { initialize as SerialportChannelInitialize } from '../serialport/channel'
import { MainSqlite } from '../db/main'
import { initialize as SqliteChannelsInitialize } from '../db/channel'
import Logging from '../utils/logging'
import fs from 'node:fs'
import path from 'node:path'
import ExcelJS from 'exceljs'

const { getLogger } = Logging()
const logger = getLogger()
let serialport: MainSerialPort
const sqlite = new MainSqlite()

// The built directory structure
//
// ├─┬ dist-electron
// │ ├─┬ main
// │ │ └── index.js    > Electron-Main
// │ └─┬ preload
// │   └── index.js    > Preload-Scripts
// ├─┬ dist
// │ └── index.html    > Electron-Renderer
//
process.env.DIST_ELECTRON = join(__dirname, '..')
process.env.DIST = join(process.env.DIST_ELECTRON, '../dist')
process.env.PUBLIC = process.env.VITE_DEV_SERVER_URL
  ? join(process.env.DIST_ELECTRON, '../public')
  : process.env.DIST

// Disable GPU Acceleration for Windows 7
if (release().startsWith('6.1')) app.disableHardwareAcceleration()

// Set application name for Windows 10+ notifications
if (process.platform === 'win32') app.setAppUserModelId(app.getName())

if (!app.requestSingleInstanceLock()) {
  app.quit()
  process.exit(0)
}

// Remove electron security warnings
// This warning only shows in development mode
// Read more on https://www.electronjs.org/docs/latest/tutorial/security
// process.env['ELECTRON_DISABLE_SECURITY_WARNINGS'] = 'true'

let win: BrowserWindow | null = null
// Here, you can also use other preload
const preload = join(__dirname, '../preload/index.js')
const url = process.env.VITE_DEV_SERVER_URL
const indexHtml = join(process.env.DIST, 'index.html')

let sqlInitTimeStart = 0
let sqlInitTimeEnd = 0

function serialportInitialize(window: BrowserWindow) {
  try {
    serialport = new MainSerialPort(window, logger)
    serialport.init()
    SerialportChannelInitialize(serialport)
  } catch (error) {
    logger.error(`serialport: open serialport error ${error}`)
  }
}

// 用来调用初始化 SQLite 的方法
async function initializeSQL(
  userDataPath: string
): Promise<{ ok: true; error: undefined } | { ok: false; error: Error }> {
  // 全局变量 用于记录开始时间
  sqlInitTimeStart = Date.now()

  try {
    await sqlite.initialize({ configDir: userDataPath, logger: logger })
  } catch (error: unknown) {
    if (error instanceof Error) {
      return { ok: false, error }
    }

    return {
      ok: false,
      error: new Error(`initializeSQL: Caught a non-error '${error}'`)
    }
  } finally {
    // 全局变量 用于记录结束时间
    sqlInitTimeEnd = Date.now()
  }

  return { ok: true, error: undefined }
}

async function createWindow() {
  // 隐藏顶部默认菜单导航
  Menu.setApplicationMenu(null)

  win = new BrowserWindow({
    title: 'Main window',
    icon: join(process.env.PUBLIC, 'nova.ico'),
    minimizable: false,
    resizable: false,
    frame: false,
    maximizable: true,
    // fullscreen: true,
    webPreferences: {
      preload,
      // Warning: Enable nodeIntegration and disable contextIsolation is not secure in production
      // Consider using contextBridge.exposeInMainWorld
      // Read more on https://www.electronjs.org/docs/latest/tutorial/context-isolation
      nodeIntegration: true,
      nodeIntegrationInWorker: true,
      contextIsolation: true
    },
    backgroundColor: '#ffffff',
    // backgroundColor: '#036a80',
    show: false // 初始设置为隐藏窗口
  })
  win.maximize()

  // 在内容加载完成后显示窗口
  // win.once('ready-to-show', () => {
  //   win.show()
  // })

  if (process.env.VITE_DEV_SERVER_URL) {
    // electron-vite-vue#298
    await win.loadURL(url)
    // Open devTool if the app is not packaged
    win.webContents.openDevTools()
  } else {
    await win.loadFile(indexHtml)
    // win.webContents.openDevTools()
  }

  win.show()

  // Test actively push message to the Electron-Renderer
  win.webContents.on('did-finish-load', () => {
    win?.webContents.send('main-process-message', new Date().toLocaleString())
  })

  // Make all links open with the browser, not with the application
  win.webContents.setWindowOpenHandler(({ url }) => {
    if (url.startsWith('https:')) shell.openExternal(url)
    return { action: 'deny' }
  })

  // 监听渲染进程崩溃
  win.webContents.on('render-process-gone', (event, details) => {
    logger.error('Renderer process crashed:', details)
    console.error('=== RENDERER PROCESS CRASHED ===')
    console.error('Reason:', details.reason)
    console.error('Exit code:', details.exitCode)
    console.error('================================')

    // 可以选择重新加载页面或显示错误信息
    if (details.reason === 'crashed') {
      logger.error('Renderer crashed, attempting to reload...')
      win.reload()
    }
  })

  // 监听渲染进程无响应
  win.webContents.on('unresponsive', () => {
    logger.error('Renderer process became unresponsive')
    console.error('=== RENDERER PROCESS UNRESPONSIVE ===')
  })

  // 监听渲染进程恢复响应
  win.webContents.on('responsive', () => {
    logger.info('Renderer process became responsive again')
    console.log('=== RENDERER PROCESS RESPONSIVE ===')
  })

  // 监听控制台消息
  win.webContents.on(
    'console-message',
    (event, level, message, line, sourceId) => {
      const logLevel = ['verbose', 'info', 'warning', 'error'][level] || 'info'
      logger[logLevel](`Console [${sourceId}:${line}]: ${message}`)
    }
  )

  // 初始化串口
  serialportInitialize(win)

  // 初始化数据库连接
  const userDataPath = app.getPath('userData')
  const sqlInitPromise = initializeSQL(userDataPath)

  const timeout = new Promise(resolve => setTimeout(resolve, 3000, 'timeout'))

  Promise.race([sqlInitPromise, timeout])
    .then(maybeTimeout => {
      if (maybeTimeout !== 'timeout') return

      /** 这里可以加载 loading 过渡 */
      logger.info(
        'sql.initialize is taking more than three seconds; showing loading dialog'
      )
    })
    .catch(err => console.error(err))

  const { ok, error: sqlError } = await sqlInitPromise

  logger.info(`sqlite init result : ${ok}`)

  if (sqlError) {
    logger.error('sql.initialize was unsuccessful; returning early')
    return
  }
  SqliteChannelsInitialize(sqlite)
}

// 注释掉硬件加速禁用，因为 Cornerstone.js 需要 WebGL 支持
// app.disableHardwareAcceleration()

// 如果需要在某些情况下禁用硬件加速，可以使用环境变量控制
if (process.env.DISABLE_HARDWARE_ACCELERATION === 'true') {
  console.log('Hardware acceleration disabled by environment variable')
  app.disableHardwareAcceleration()
}

app.whenReady().then(createWindow)

function quit() {
  if (serialport) serialport.close()
  if (sqlite) sqlite.close()
  win = null
  if (process.platform !== 'darwin') app.quit()
}

app.on('window-all-closed', () => {
  try {
    quit()
  } catch (error) {
    logger.error(`quit : error ${error}`)
  }
})

app.on('second-instance', () => {
  if (win) {
    // Focus on the main window if the user tried to open another
    if (win.isMinimized()) win.restore()
    win.focus()
  }
})

app.on('activate', () => {
  const allWindows = BrowserWindow.getAllWindows()
  if (allWindows.length) {
    allWindows[0].focus()
  } else {
    createWindow()
  }
})

// New window example arg: new windows url
ipcMain.handle('open-win', (_, arg) => {
  const childWindow = new BrowserWindow({
    webPreferences: {
      preload,
      nodeIntegration: true,
      contextIsolation: false
    }
  })

  if (process.env.VITE_DEV_SERVER_URL) {
    childWindow.loadURL(`${url}#${arg}`)
  } else {
    childWindow.loadFile(indexHtml, { hash: arg })
  }
})

ipcMain.handle('quit-app', () => {
  quit()
})

let pdfWindow: BrowserWindow | null = null
// 导出pdf
ipcMain.handle('export-pdf', async (_event, obj) => {
  // const data = await win.webContents.printToPDF({ pageSize: 'A4' })
  const pdfPath = path.join(obj.casePath, `${obj.nameWithNo}.pdf`)
  const dirExist = fs.existsSync(obj.casePath)
  if (!dirExist) {
    fs.mkdirSync(obj.casePath)
  }

  fs.writeFile(pdfPath, obj.buffer, 'utf8', error => {
    logger.info('export pdf result : ', error)
    if (error) {
      logger.error('pdf export error : ')
      logger.error(error)
      win.webContents.send('export-pdf-res', {
        success: false,
        message: `导出失败，路径：${pdfPath}`
      })
      throw error
    } else {
      win.webContents.send('export-pdf-res', {
        success: true,
        message: `导出成功，路径：${pdfPath}`
      })
    }
  })
})

// 删除pdf
ipcMain.handle('remove-pdf', async (_event, obj) => {
  logger.info('receive data : ', obj)
  const pdfPath = path.join(obj.casePath, `${obj.nameWithNo}.pdf`)
  const dirExist = fs.existsSync(pdfPath)
  if (dirExist) {
    fs.rmSync(pdfPath)
  }
})

ipcMain.handle('export-excel', async (_event, obj) => {
  const workbook = new ExcelJS.Workbook()
  const worksheet = workbook.addWorksheet('Sheet1')
  const { patientCases, casePath, nameWithNo } = obj

  // 设置列宽
  worksheet.columns = [
    { width: 60 }, // A
    { width: 50 } // B
  ]

  let currentRow = 1 // 跟踪当前行号

  // 处理每组数据
  for (let i = 0; i < patientCases.length; i++) {
    const patientInfo = patientCases[i]

    // 医院名称
    worksheet.mergeCells(`A${currentRow}:B${currentRow}`)
    const hospitalCell = worksheet.getCell(`A${currentRow}`)
    hospitalCell.value = patientInfo.hospitalName
    hospitalCell.alignment = { horizontal: 'center', vertical: 'middle' }
    hospitalCell.font = { size: 18, bold: true }

    // 病人基本信息
    worksheet.getCell(`A${currentRow + 1}`).value = '姓名：' + patientInfo.name

    worksheet.getCell(`A${currentRow + 2}`).value =
      '性别：' + (patientInfo.sex === 0 ? '男' : '女')

    worksheet.getCell(`A${currentRow + 3}`).value = '年龄：' + patientInfo.age

    worksheet.getCell(`A${currentRow + 4}`).value =
      '病历编号：' + patientInfo.sn

    worksheet.getCell(`A${currentRow + 5}`).value =
      '治疗日期：' + patientInfo.createdTime

    // 底部治疗信息
    const timeCell = worksheet.getCell(`A${currentRow + 6}`)
    timeCell.value = `治疗方案\n时间：${patientInfo.time} min  辐射器：${
      patientInfo.radiator || ''
    }`
    timeCell.alignment = {
      horizontal: 'left',
      vertical: 'middle',
      wrapText: true
    }

    const detailCell = worksheet.getCell(`A${currentRow + 7}`)
    detailCell.value = patientInfo.detail
    detailCell.alignment = {
      horizontal: 'left',
      vertical: 'middle',
      wrapText: true
    }
    worksheet.getRow(currentRow + 7).height = 180 // 为病历详情设置更大的行高

    // 合并图片区域的单元格 (B2:B8)
    worksheet.mergeCells(`B${currentRow + 1}:B${currentRow + 7}`)

    // 为合并后的单元格设置样式
    const mergedImageCell = worksheet.getCell(`B${currentRow + 1}`)
    mergedImageCell.alignment = {
      vertical: 'middle',
      horizontal: 'center'
    }

    // 添加波形图
    try {
      const imageId = workbook.addImage({
        base64: patientInfo.chart,
        extension: 'png'
      })

      // 在右侧添加图片
      worksheet.addImage(imageId, {
        tl: {
          col: 1, // B列
          row: currentRow, // 从当前行的第2行开始
          colWidth: 1, // 占用1列
          rowHeight: 6, // 占用7行
          worksheet: worksheet
        } as any,
        br: {
          col: 2,
          row: currentRow + 6,
          colWidth: 0,
          rowHeight: 0,
          worksheet: worksheet
        } as any,
        ext: {
          width: 50,
          height: 19
        }
      })
    } catch (error) {
      logger.error(`添加波形图失败: ${error}`)
    }

    // 设置所有单元格样式
    for (let row = currentRow; row <= currentRow + 7; row++) {
      for (let col = 1; col <= 2; col++) {
        const cell = worksheet.getCell(row, col)
        cell.font = { bold: true }
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' }
        }
      }
    }

    // 更新当前行号，加上3行分隔
    currentRow += 11 // 5行内容 + 3行分隔
  }

  try {
    const dirExist = fs.existsSync(obj.casePath)
    if (!dirExist) {
      fs.mkdirSync(obj.casePath)
    }
    const excelPath = path.join(casePath, `${nameWithNo}.xlsx`)
    if (fs.existsSync(excelPath)) {
      // 如果文件已经存在，先设置文件为只读权限
      fs.chmodSync(excelPath, 0o666)
    }
    await workbook.xlsx.writeFile(excelPath)
    fs.chmodSync(path.join(casePath, `${nameWithNo}.xlsx`), 0o444)
  } catch (error) {
    logger.error(`export excel error : ${error}`)
  }
})

// 删除pdf
ipcMain.handle('remove-excel', async (_event, obj) => {
  logger.info('receive data : ', obj)
  const pdfPath = path.join(obj.casePath, `${obj.nameWithNo}.xlsx`)

  const dirExist = fs.existsSync(pdfPath)
  if (dirExist) {
    fs.rmSync(pdfPath)
  }
})
