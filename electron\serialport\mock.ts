import { Methods } from './types'

export function mockingReceiveData(method: Methods, serialPort) {
  let buffer = Buffer.from([0x5a, 0xa5, 0x05, 0x82, 0x10, 0x01, 0x00, 0x00])

  switch (method) {
    case 'PasswordValidate.Confirm':
      buffer = Buffer.from([0x5a, 0xa5, 0x05, 0x82, 0x10, 0x01, 0x00, 0x00])
      break
    case 'PowerCalibrationPageOneSend.One':
    case 'PowerCalibrationPageOneSend.Five':
      buffer = Buffer.from([
        0x5a, 0xa5, 0x15, 0x82, 0x10, 0x30, 0x00, 0x01, 0x00, 0x05, 0x00, 0x0a,
        0x00, 0x14, 0x00, 0x28, 0x00, 0x3c, 0x00, 0x64, 0x00, 0x96, 0x00, 0xc8,
        0x5a, 0xa5, 0x1b, 0x82, 0x10, 0x20, 0x07, 0x6f, 0x09, 0xc4, 0x0a, 0xc3,
        0x0b, 0x8a, 0x0c, 0x48, 0x0c, 0xbd, 0x0d, 0x67, 0x0e, 0x12, 0x0e, 0xc6,
        0xa1, 0xc0, 0xa3, 0xb1, 0x00, 0x00
      ])
      break
    default:
      break
  }

  serialPort.port.emitData(buffer)
}
