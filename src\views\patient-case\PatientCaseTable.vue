<template>
  <div>
    <div class="flex justify-between items-center">
      <h2 class="font-bold mb-2">治疗记录</h2>
      <el-button :disabled="!router.currentRoute.value.query.patientId" type="primary" :icon="Plus"
        @click="handleCreateCase">
        新增治疗记录
      </el-button>
    </div>
    <el-table :data="patientCases" empty-text="暂无数据" style="width: 100%">
      <el-table-column prop="createdTime" label="日期" width="300" header-align="center" align="center"
        :formatter="dateFormatter" />
      <el-table-column prop="detail" label="治疗记录" header-align="center" align="center" show-overflow-tooltip />
      <el-table-column prop="creator" label="创建人" width="300" header-align="center" align="center" />
      <el-table-column label="操作" width="210" header-align="center" align="center">
        <template #default="{ row }">
          <el-button size="small" type="warning" @click="handleEdit(row)">编辑</el-button>
          <el-button size="small" type="primary" @click="handlePrint(row)">打印</el-button>
          <el-button v-if="deleteActionShow(row.createdTime)" size="small" type="danger"
            @click="handleDelete(row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <CreatePatientCaseModal ref="patientCaseModalRef" :patient="patient" @close="handleCreateCaseDone" />
  </div>
</template>
<script setup lang="ts">
import { onMounted, ref, watch } from 'vue';
import { Plus } from '@element-plus/icons-vue'
import CreatePatientCaseModal from './CreatePatientCase.vue';
import { type PatientCase, type Patient, UserRole } from '@/types/entity';
import dayjs from 'dayjs'
import { useUserStore } from '@/store/modules/user';
import { useMedicalStore } from '@/store/modules/medical';

import { type TDocumentDefinitions, type Content } from 'pdfmake/interfaces'
import * as pdfMake from "pdfmake/build/pdfmake";
// @ts-ignore
import * as pdffonts from '@/assets/vfs_fonts';
import { useRouter } from 'vue-router';

// @ts-ignore
(<any>pdfMake).vfs = (<any>pdffonts).fontVfs;


// import 'pdfmake/build/vfs_fonts';

// @ts-ignore
(<any>pdfMake).fonts = {
  SourceHanSans: {
    normal: 'SourceHanSansCN-Normal.otf',
    bold: 'SourceHanSansCN-Bold.otf',
    italics: 'SourceHanSansCN-Bold.otf',
    bolditalics: 'SourceHanSansCN-Bold.otf'
  }
}

defineOptions({
  name: 'PatientCaseTable'
})
const props = defineProps<{
  patient: Patient
}>()
const patientCases = ref<PatientCase[]>([])
const patientCaseModalRef = ref()
const userStore = useUserStore()
const medicalStore = useMedicalStore()
const router = useRouter()

const deleteActionShow = (date: Date) => {
  const today = dayjs().startOf('day')
  const createdAt = dayjs(date).startOf('day')
  console.log('today', today, createdAt, createdAt.isSame(today))

  return userStore.getUserRole !== UserRole.USER || (userStore.getUserRole === UserRole.USER && createdAt.isSame(today))
}

const query = ({ generateFile }: { generateFile: boolean } = { generateFile: false }) => {
  window.electronAPI.sqlite.queryPatientCases(props.patient.id).then((res) => {
    patientCases.value = res
    console.log('patient cases', patientCases.value)
    if (generateFile) {
      if (res.length) {
        generatePdfAndExcel(res)
      } else {
        window.electronAPI.sqlite.removePdf({
          casePath: `${userStore.getCasePath}/${userStore.getLoginName}`,
          nameWithNo: `${props.patient.name}(${props.patient.sn})`
        })
        window.electronAPI.sqlite.removeExcel({
          casePath: `${userStore.getCasePath}/${userStore.getLoginName}`,
          nameWithNo: `${props.patient.name}(${props.patient.sn})`
        })
      }
    }
  })
}

const dateFormatter = (_row: any, _column: any, cellValue: Date) => {
  return dayjs(cellValue).format('YYYY年M月D日   HH:mm:ss')
}

watch(() => props.patient, (newVal) => {
  console.log('patientId', newVal)
  if (newVal) {
    query()
  }
}, { immediate: true })

const handleCreateCase = () => {
  // ElMessage.info('新增治疗记录功能正在开发中')
  if (!medicalStore.getCureParams) {
    ElMessage.info('请先完成治疗，再新增治疗记录')
    return
  }

  if (patientCaseModalRef.value) {
    patientCaseModalRef.value.open()
  }
}

const handleCreateCaseDone = () => {
  query({ generateFile: true })
}

const handleEdit = (row: PatientCase) => {
  console.log('edit', row)
  if (patientCaseModalRef.value) {
    patientCaseModalRef.value.open(row)
  }
}

const handleDelete = (id: number) => {
  window.electronAPI.sqlite.deletePatientCase(id).then(() => query({ generateFile: true }))
}

const handlePrint = async (row: PatientCase) => {
  console.log('print', row)
  // window.electronAPI.print.printPatientCase(ro)

  const query = {
    ...row,
  }

  const pdfDocGenerator = await generatePdf([row])
  pdfDocGenerator.getDataUrl(dataUrl => {
    medicalStore.setPdfContent(dataUrl)
    medicalStore.setPrintParams(query as any)
    router.push({ path: '/microwave/medical-record-print', query: { patientId: props.patient.id } })
  })
}

async function updateCaseFiles() {
  patientCases.value.length && generatePdfAndExcel(patientCases.value)
}

async function generatePdfAndExcel(rows: PatientCase[]) {
  try {
    // 保存pdf
    const pdfDocGenerator = await generatePdf(rows)
    pdfDocGenerator.getBuffer(buffer => {
      window.electronAPI.sqlite.exportPdf({
        casePath: `${userStore.getCasePath}\\${userStore.getLoginName}`,
        nameWithNo: `${props.patient.name}(${props.patient.sn})`,
        buffer
      })
    })
    // 保存excel
    window.electronAPI.sqlite.exportExcel({
      casePath: `${userStore.getCasePath}\\${userStore.getLoginName}`,
      nameWithNo: `${props.patient.name}(${props.patient.sn})`,
      patientCases: rows.map(item => {
        const cureParams = item.cureParams ? JSON.parse(item.cureParams) : {}
        return {
          ...item,
          name: props.patient.name,
          age: props.patient.age,
          sex: props.patient.sex,
          sn: props.patient.sn,
          createdTime: dateFormatter(undefined, undefined, item.createdTime),
          radiator: cureParams.radiator,
          time: cureParams.time
        }
      })
    })
  } catch (error) {

  }
}

async function generatePdf(rows: PatientCase[]): Promise<pdfMake.TCreatedPdf> {
  const content: Content = []

  for (let index = 0; index < rows.length; index++) {
    const info = rows[index]
    content.push({
      stack: [info.hospitalName || userStore.getHospitalName],
      style: 'header',
      pageBreak: index === 0 ? undefined : 'before'
    })
    content.push({
      canvas: [
        {
          type: 'line',
          x1: 0,
          y1: 10,
          x2: 510,
          y2: 10,
          lineWidth: 1,
          lineColor: '#000000',
        },
      ],
    })
    content.push({
      style: 'onelineTable',
      table: {
        headerRows: 0,
        widths: [200, 150, 150],
        body: [
          [`姓名：${props.patient.name}`, `性别：${props.patient.sex === 0 ? '男' : '女'}`, `年龄：${props.patient.age}`],
        ]
      },
      layout: 'noBorders'
    })
    content.push({
      style: 'onelineTable',
      table: {
        headerRows: 0,
        widths: [280, 220],
        body: [
          [`病历编号：${props.patient.sn}`, `治疗日期：${dateFormatter(undefined, undefined, info.createdTime)}`],
        ]
      },
      layout: 'noBorders'
    })
    content.push({
      style: 'onelineTable',
      table: {
        headerRows: 0,
        widths: [500],
        heights: [200],
        body: [
          [
            {
              image: info.chart,
              width: 480,
              height: 180,
              // fit: [480, 180],
            }
          ]
        ]
      },
    })

    // 治疗参数
    const cureContent: string[] = []
    const cureWidth: number[] = []
    const cureParams: any = JSON.parse(info.cureParams || "")
    cureContent.push(`时间：${cureParams.time} min`)
    cureWidth.push(150)

    cureContent.push(`辐射器：${cureParams.radiator || ' '}`)
    cureWidth.push(150)

    content.push({
      style: 'onelineTable',
      table: {
        headerRows: 0,
        widths: cureWidth,
        body: [
          cureContent,
        ]
      },
      layout: 'noBorders'
    })

    // 治疗记录
    content.push({
      stack: ['患者病历详细治疗记录'],
      style: 'subHeader'
    })
    content.push({
      canvas: [
        {
          type: 'line',
          x1: 0,
          y1: 10,
          x2: 510,
          y2: 10,
          lineWidth: 1,
          lineColor: '#000000',
        },
      ],
      marginBottom: 20
    })
    // console.log('form detail : ', form.detail, form.detail.split(/\n/).map(item => item.replace(/ /, '  ')).join('\n'))
    let detail = ''
    try {
      detail = info.detail.split(/\n/).map((item: string) => item.replace(/\ /g, '　')).join('\n')
    } catch (error) {
      console.error('format form detail error : ')
      console.error(error)
      detail = info.detail
    }
    content.push({
      text: detail,
      style: {
        preserveLeadingSpaces: true
      },
      margin: [0, 0, 0, 20] // 确保签名行上方有足够的空间
    })


    // content.push({
    //   text: `签名：______________`,
    //   absolutePosition: { x: 500, y: 750 }
    // })
    content.push({
      stack: [
        {
          text: '签名：',
          style: 'signatureText'
        },
        {
          canvas: [
            {
              type: 'line',
              x1: 40,
              y1: 0,
              x2: 130, // 留白区域的长度
              y2: 0,
              lineWidth: 1,
              lineColor: 'black'
            }
          ],
          margin: [0, -2, 0, 0], // 调整线条与文本的间距
          style: 'signatureLine'
        }
      ],
      absolutePosition: { x: 430, y: 760 } // 设置绝对位置
    })
  }

  const dd: TDocumentDefinitions = {
    content,
    styles: {
      header: {
        fontSize: 18,
        // bold: true,
        alignment: 'center',
        margin: [0, 0, 0, 0],
      },
      subHeader: {
        fontSize: 15,
        // bold: true,
        alignment: 'center',
        margin: [0, 0, 0, 0],
      },
      pageHeader: {
        fontSize: 14,
        // bold: true,
        alignment: 'center',
      },
      onelineTable: {
        margin: [0, 5, 0, 5]
      },
      assign: {
        margin: [0, 100, 0, 0],
        fontSize: 15
      }
    },
    defaultStyle: {
      font: 'SourceHanSans'
    },
    pageMargins: [40, 70, 40, 60],
    header: (currentPage) => {
      return null
      // if (currentPage === 1) {
      //   return null
      // } else {
      //   return [
      //     {
      //       stack: ['患者病历详细治疗记录'],
      //       style: 'pageHeader',
      //       marginTop: 30
      //     },
      //     {
      //       canvas: [
      //         {
      //           type: 'line',
      //           x1: 40,
      //           y1: 10,
      //           x2: 545,
      //           y2: 10,
      //           lineWidth: 1,
      //           lineColor: '#000000',
      //         },
      //       ],
      //       marginBottom: 10
      //     }
      //   ]
      // }
    },
    footer: (currentPage, pageCount) => {
      return [
        // currentPage === pageCount ?
        //   {
        //     columns: [
        //       {
        //         text: '  ',
        //         alignment: 'right',
        //         marginLeft: 5
        //       },
        //       {
        //         columns: [
        //           {
        //             text: '签名：',
        //             alignment: 'right',
        //           },
        //           {
        //             text: '______________',
        //             alignment: 'left',
        //             decoration: 'underline',
        //             marginLeft: 10
        //           }
        //         ]
        //       }
        //     ]
        //   } :
        //   {
        //     text: " "
        //   },
        {
          text: `第 ${currentPage.toString()} 页，共 ${pageCount.toString()} 页`,
          alignment: 'center',
          margin: [0, 25, 20, 0],
          style: {
            fontSize: 10
          }
        }
      ]
    }
  }

  return pdfMake.createPdf(dd)
}

onMounted(() => {
  window.electronAPI.serialport.receive('export-pdf-res', (_, args) => {
    console.log('args : ', args)

  })
})

defineExpose({
  updateCaseFiles
})
</script>
