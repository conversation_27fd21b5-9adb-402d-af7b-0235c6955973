/**
 * @see https://www.electron.build/configuration/configuration
 */
{
  appId: 'com.nuowan.controlprogram.NW8000',
  asar: true,
  icon: 'public/nova.ico',
  directories: {
    output: 'release/${version}'
  },
  productName: '诺万医疗',
  files: ['dist-electron', 'dist', 'public/assets'],
  mac: {
    artifactName: '${productName}_${version}.${ext}',
    target: ['dmg']
  },
  win: {
    target: [
      {
        target: 'nsis',
        arch: ['x64']
      }
    ],
    artifactName: '${productName}-NW8000-${version}.${ext}'
  },
  nsis: {
    oneClick: false,
    perMachine: false,
    allowToChangeInstallationDirectory: true,
    deleteAppDataOnUninstall: false
  }
}
