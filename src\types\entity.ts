import type { FindOptionsOrder } from 'typeorm'

export type UserQueryCriteria = {
  isSuper?: boolean
  keyword?: string
  role?: UserRole
  order?: FindOptionsOrder<User>
}

export enum UserRole {
  SUPER = 'Super',
  ADMIN = 'Admin',
  USER = 'User'
}

export interface User {
  id: number
  loginName: string
  name: string | null
  password: string
  role: UserRole
  latestLoginTime: Date | null
  createdTime: Date
  updateTime: Date
  deletedTime: Date | null
}

export interface Patient {
  id: number
  name: string
  sn: string
  age: number
  sex: number
  weight?: string
  doctorName?: string
  // cases: PatientCase[]
  createdTime: Date
  updateTime: Date
  deletedTime: Date | null
}

export interface PatientCase {
  id: number
  chart: string | null
  cureParams: string | null
  detail: string
  hospitalName?: string
  patient: Patient
  creatorId?: number
  creator?: string
  createdTime: Date
  updateTime: Date
  deletedTime: Date | null
}
