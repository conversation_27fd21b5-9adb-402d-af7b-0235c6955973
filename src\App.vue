<script setup lang="ts">
import { unref, onMounted, onUnmounted } from 'vue';
import { getPageNoBuffer, PageNoEnum } from '@/utils'
import { useRouter } from 'vue-router';
import { WarningFilled } from '@element-plus/icons-vue'
import { useCalibrationStore } from '@/store/modules/calibration'
import { useSerialportStore } from '@/store/modules/serialport'
import { useUserStore } from '@/store/modules/user';

const router = useRouter()
const calibrationStore = useCalibrationStore()
const serialportStore = useSerialportStore()
const userStore = useUserStore()

onMounted(() => {
  console.log('Application init ready')

  // 接受下位机查询页面命令后，返回Home页面页码（当前为固定返回Home页面页码）
  window.electronAPI.serialport.receive('send-page-no', () => {
    if (unref(router.currentRoute).fullPath === '/home') {
      // 发送当前页码
      window.electronAPI.serialport.dispatch('WavePage.SendPageNo', getPageNoBuffer(PageNoEnum.Home))
    }
  })

  // 15秒未收到数据，判断为切换至触摸屏，强制返回Home页面，同时判断为
  window.electronAPI.serialport.receive('switch-touch-screen', (_, { switched, ready }: { switched: boolean, ready: boolean }) => {
    console.info('receive ready : ', ready, switched)
    // 增加是否登录的判断
    // 未登录时，如果当前不是用户登录页（比如在厂家登录页，或者其他极端异常情况），则跳转至登录页
    if (userStore.getLoginName === null) {
      if (unref(router.currentRoute).fullPath !== '/login') {
        router.replace('/login')
      }
    } else {
      // 已登录时，则执行和之前一样的逻辑
      if (ready !== undefined) {
        if (!ready) {
          serialportStore.setSwitched(true)
          router.replace('/home')
          ElMessage({
            message: '串口连接已断开',
            type: 'warning',
            grouping: true,
          })
        }

        serialportStore.setReady(ready)
      }

      if (switched !== undefined) {
        serialportStore.setSwitched(switched)
        if (switched) {
          router.replace('/home')
          calibrationStore.setFirstIn(true)
        }
      }
    }
  })

  // 查询serialport连接状态
  setTimeout(() => {
    window.electronAPI.serialport.dispatch('StartingUp.GetReadyStatus')
  }, 2000)

  // 检查病历存储路径
  setTimeout(() => {
    window.electronAPI.sqlite.checkCasePath()
  }, 2000)
})

onUnmounted(() => {
  window.electronAPI.serialport.removeListener('send-page-no')
  window.electronAPI.serialport.removeListener('switch-touch-screen')
})
</script>

<template>
  <router-view></router-view>
  <notifications group="default" position="top center" class="!top-8 vue-notifications-wrap" :width="350" :max="3" />
  <notifications group="bottom" position="bottom center" class="!bottom-8 vue-notifications-wrap" :width="300" :max="1">
    <template #body="{ item }">
      <div
        :class="['vue-notification-template', '!bottom-8 vue-notifications-wrap', 'el-message el-message--warning nova-bottom-toast']">
        <el-icon v-if="item.type === 'warn'" class="el-message__icon el-message-icon--warning">
          <warning-filled />
        </el-icon>
        <p class="el-message__content" v-html="item.text" />
      </div>
    </template>
  </notifications>
</template>

<style>
.vue-notifications-wrap .notification-content {
  font-size: 22px;
}
</style>
