import { defineStore } from 'pinia'
import { store } from '@/store'

import type { ICalibrationState } from '../types'

export const useCalibrationStore = defineStore({
  id: 'calibration-store',
  state: (): ICalibrationState => ({
    firstIn: true
  }),
  getters: {
    getFirstIn(state): boolean {
      return state.firstIn
    }
  },
  actions: {
    setFirstIn(data: boolean) {
      this.firstIn = data
    }
  }
})

export function useCalibrationStoreHook() {
  return useCalibrationStore(store)
}
