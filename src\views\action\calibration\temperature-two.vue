<script setup lang="ts">
import { ref, onMounted, onUnmounted, reactive } from 'vue'
import PageWrapper from '@/components/PageWrapper/index.vue'
import { useRouter } from 'vue-router'
import { useCalibrationStore } from '@/store/modules/calibration'
import { formatTemperature } from '@/utils'

import { type MessageHandler } from 'element-plus'

defineOptions({
  name: 'TemperatureCalibrationPageTwo'
})

const router = useRouter()
const calibrationStore = useCalibrationStore()
const firstInActionStack = ref<string[]>([])
const temperatureStack = ref<[string, string][]>([])
let timer: NodeJS.Timeout | undefined = undefined
let loadingInstance: any

function clearLoading() {
  if (loadingInstance ) {
    loadingInstance .close()
  }
}

function goBack() {
  if (calibrationStore.getFirstIn) {
    firstInActionStack.value.push('NotSaveExit')
  } else {
    loadingInstance  = ElLoading.service({
      lock: true,
      text: '下发中...',
      background: 'rgba(0, 0, 0, 0.7)',
    })
    setTimeout(() => {
      loadingInstance ?.close()
    }, 1300)
    setTimeout(() => {
      window.electronAPI.serialport.dispatch('TemperatureCalibrationPageTwoSend.NotSaveExit')
    }, 1000)
  }
}

function handleSave() {
  console.log('click save')
  if (calibrationStore.getFirstIn) {
    firstInActionStack.value.push('SaveExit')
  } else {
    loadingInstance  = ElLoading.service({
      lock: true,
      text: '下发中...',
      background: 'rgba(0, 0, 0, 0.7)',
    })
    setTimeout(() => {
      loadingInstance ?.close()
    }, 1300)
    setTimeout(() => {
      window.electronAPI.serialport.dispatch('TemperatureCalibrationPageTwoSend.SaveExit')
    }, 1000)
  }
}

const calibrationTemperature = ref('')

const temperature = reactive({
  low: '',
  middle: '',
  high: ''
})
type CalibrationType = 'low' | 'middle' | 'high'
function handleCalibration(type: CalibrationType) {
  let instruction = 'LowTemperatureSubmit'

  if (type === 'middle') {
    instruction = 'MidTemperatureSubmit'
  }

  if (type === 'high') {
    instruction = 'HighTemperatureSubmit'
  }

  if (calibrationStore.getFirstIn) {
    firstInActionStack.value.push(instruction)
  } else {
    performCalibration(instruction)
  }
}

function performCalibration(instruction: string) {
  setTimeout(() => {
    window.electronAPI.serialport.dispatch(`TemperatureCalibrationPageTwoSend.${instruction}`)
  }, 500)
}

const temperatureReg = /^([34]{1}[0-9]{1})(.[0-9]{0,2})?$/
function handleTempChange(type: CalibrationType) {
  let instruction = ''
  if (type === 'low') {
    if (Number(temperature.low) > 34 || Number(temperature.low) < 32 || !temperatureReg.test(temperature.low)) {
      temperature.low = ''
      return
    }
    instruction = 'LowTemperatureConfirm'
  }

  if (type === 'middle') {
    if (Number(temperature.middle) > 41 || Number(temperature.middle) < 39 || !temperatureReg.test(temperature.middle)) {
      temperature.middle = ''
      return
    }
    instruction = 'MidTemperatureConfirm'
  }

  if (type === 'high') {
    if (Number(temperature.high) > 48 || Number(temperature.high) < 46 || !temperatureReg.test(temperature.high)) {
      temperature.high = ''
      return
    }
    instruction = 'HighTemperatureConfirm'
  }

  let temp = temperature[type]

  if (calibrationStore.getFirstIn) {
    temperatureStack.value.push([instruction, temp])
  } else {
    performSendTemperature(instruction, temp)
  }
}

function performSendTemperature(instruction: string, temp: string) {
  let buffer = []
  if (!temp) {
    buffer = [0x00, 0x00]
  } else {
    if (!temp.includes('.')) {
      temp = temp.slice(0, 2) + '.' + temp.slice(2)
    }

    if (temp.length > 5) {
      temp = temp.slice(0, 5)
    }

    if (temp.length < 5) {
      temp = temp.padEnd(5, '0')
    }

    temp = temp.replace('.', '')

    const hexStr = Number(temp).toString(16).padStart(4, '0')

    buffer = [hexStr.slice(0, 2), hexStr.slice(2, 4)].map(t => parseInt(t, 16))
  }
  window.electronAPI.serialport.dispatch(`TemperatureCalibrationPageTwoSend.${instruction}`, buffer)
}

function handleTempInput(type: CalibrationType) {
  if (type === 'low' && temperature.low.length > 5) {
    temperature.low = temperature.low.slice(0, 5)
  } else if (type === 'middle' && temperature.middle.length > 5) {
    temperature.middle = temperature.middle.slice(0, 5)
  } else {
    temperature.high = temperature.high.slice(0, 5)
  }
}

let orderFaultInstance: MessageHandler | undefined
let sensorFaultInstance: MessageHandler | undefined
let communicationFaultInstance: MessageHandler | undefined
onMounted(() => {
  window.electronAPI.serialport.receive('temperature-two-display', (_, { orderFault, sensorFault, communicationFault, temperatureTwo, exit }: { orderFault?: boolean, sensorFault?: boolean, communicationFault?: boolean, temperatureTwo?: string, exit?: boolean }) => {
    if (orderFault !== undefined) {
      if (orderFault) {
        orderFaultInstance = ElMessage({
          showClose: false,
          message: '测温出错，请从低温开始校准',
          type: 'warning',
          grouping: true
        })
      } else {
        if (orderFaultInstance) {
          orderFaultInstance.close()
          orderFaultInstance = undefined
        }
      }
    }

    if (sensorFault !== undefined) {
      if (sensorFault) {
        if (!sensorFaultInstance) {
          sensorFaultInstance = ElMessage({
            showClose: false,
            message: '测温传感器故障',
            type: 'warning',
            duration: 0,
            grouping: true
          })
        }

        handleUnrecognizedTemperature()
      } else {
        if (sensorFaultInstance) {
          sensorFaultInstance.close()
          sensorFaultInstance = undefined
        }
      }
    }

    if (communicationFault !== undefined) {
      if (communicationFault) {
        if (!communicationFaultInstance) {
          communicationFaultInstance = ElMessage({
            showClose: false,
            message: '测温通讯故障',
            type: 'warning',
            duration: 0,
            grouping: true
          })
        }

        handleUnrecognizedTemperature()
      } else {
        if (communicationFaultInstance) {
          communicationFaultInstance.close()
          communicationFaultInstance = undefined
        }
      }
    }

    if (temperatureTwo) {
      calibrationTemperature.value = formatTemperature(temperatureTwo)
      if (calibrationTemperature.value === '0') {
        calibrationTemperature.value = '0.00'
      } else {
        if (calibrationStore.getFirstIn) {
          calibrationStore.setFirstIn(false)
          if (temperatureStack.value.length) {
            const [lastTemperatureInstruction, temperature] = temperatureStack.value[temperatureStack.value.length - 1]
            console.log('lastTemperature, buffer : ', lastTemperatureInstruction, temperature)
            performSendTemperature(lastTemperatureInstruction, temperature)
          }
          if (firstInActionStack.value.length) {
            const calibrationActions = firstInActionStack.value.filter(x => x !== 'NotSaveExit' && x !== 'SaveExit')
            if (calibrationActions.length) {
              const firstCalibrationInstruction = calibrationActions[0]
              performCalibration(firstCalibrationInstruction)
            } else {
              const lastAction = firstInActionStack.value[firstInActionStack.value.length - 1]
              if (lastAction === 'NotSaveExit') {
                goBack()
              } else {
                handleSave()
              }
            }
          }
        }

        clearWaitTimer()
      }
    }

    // 收到指令后再做退出操作
    console.log('receive exit : ', exit)
    if (exit === true) {
      setTimeout(() => {
        router.back()
      }, 300)
    }
  })

  // 开启T2温度校准界面循环解析
  window.electronAPI.serialport.dispatch('CurrentPage.TemperatureCalibrationPageTwoSend')

  // 首次进入页面45秒定时(下位机40秒，上位机取45秒，保持一定间隔，避免阻塞导致的延迟影响)
  timer = setTimeout(() => {
    handleUnrecognizedTemperature()
  }, 45 * 1000)
})

onUnmounted(() => {
  window.electronAPI.serialport.removeListener('temperature-two-display')

  if (orderFaultInstance) {
    orderFaultInstance.close()
    orderFaultInstance = undefined
  }

  if (sensorFaultInstance) {
    sensorFaultInstance.close()
    sensorFaultInstance = undefined
  }

  if (communicationFaultInstance) {
    communicationFaultInstance.close()
    communicationFaultInstance = undefined
  }

  firstInActionStack.value = []
  temperatureStack.value = []
  clearWaitTimer()
  clearLoading()
})

function clearWaitTimer() {
  if (timer) {
    clearTimeout(timer)
    timer = undefined
  }
}

function handleUnrecognizedTemperature() {
  if (calibrationStore.getFirstIn) {
    calibrationStore.setFirstIn(false)
    // 注意，此时温度数据未识别，故temperatureStack中的校准按钮操作不执行，否则会异常，如有退出操作，只执行退出操作
    if (firstInActionStack.value.length) {
      const logoutActions = firstInActionStack.value.filter(x => x === 'NotSaveExit' || x === 'SaveExit')
      if (logoutActions.length) {
        const lastAction = firstInActionStack.value[logoutActions.length - 1]
        if (lastAction === 'NotSaveExit') {
          goBack()
        } else {
          handleSave()
        }
      }
    }
  }

  clearWaitTimer()
}
</script>

<template>
  <PageWrapper show-logo :show-logout="false">
    <div class="content-wrapper flex-center h-[800px] relative">
      <div class="flex items-center absolute right-52 top-36">
        <span class="mr-2 text-gray-900">T2</span>
        <span class="w-20 h-8 rounded bg-base-100 text-center inner-shadow">{{ calibrationTemperature }}</span>
        <span class="ml-2 text-gray-900">℃</span>
      </div>
      <div class="h-full pt-36 text-gray-900 text-xl">
        <p class="text-4xl text-center mb-20 font-bold">T2校准温度</p>
        <div class="flex items-center mb-10">
          <span class="mr-6">1、输入温度计低温读数（32.0 - 34.0）</span>
          <input v-model="temperature.low" type="text"
            class="input input-sm input-primary inner-shadow border-none w-24 text-gray-900"
            @change="handleTempChange('low')" @input="handleTempInput('low')">
          <button class="temperature-calibration-button" @click="handleCalibration('low')">校准低温</button>
        </div>
        <div class="flex items-center mb-10">
          <span class="mr-6">2、输入温度计中温读数（39.0 - 41.0）</span>
          <input v-model="temperature.middle" type="text"
            class="input input-sm input-primary inner-shadow border-none w-24 text-gray-900"
            @change="handleTempChange('middle')" @input="handleTempInput('middle')">
          <button class="temperature-calibration-button" @click="handleCalibration('middle')">校准中温</button>
        </div>
        <div class="flex items-center mb-10">
          <span class="mr-6">3、输入温度计高温读数（46.0 - 48.0）</span>
          <input v-model="temperature.high" type="text"
            class="input input-sm input-primary inner-shadow border-none w-24 text-gray-900"
            @change="handleTempChange('high')" @input="handleTempInput('high')">
          <button class="temperature-calibration-button" @click="handleCalibration('high')">校准高温</button>
        </div>
        <div class="mt-20 flex justify-center">
          <button class="btn btn-sm btn-primary rounded mr-20 text-white px-6 h-10" @click="handleSave">保存并退出</button>
          <button class="btn btn-sm btn-accent rounded px-6 h-10" @click="goBack">不保存并退出</button>
        </div>
      </div>
    </div>
  </PageWrapper>
</template>
