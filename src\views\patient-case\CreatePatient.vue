<template>
  <el-dialog v-model="visible" :title="title" width="30%" :before-close="reset">
    <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
      <el-form-item label="姓名:" prop="name">
        <el-input v-model="form.name" placeholder="请输入患者姓名" clearable></el-input>
      </el-form-item>
      <el-form-item label="病历编号:" prop="sn">
        <!-- <input :value="form.sn" @input="handleInput" /> -->
        <el-input ref="snInputRef" v-model="form.sn" placeholder="请输入病历编号" clearable
          @keydown="handleInputChange"></el-input>
      </el-form-item>
      <el-form-item label="性别:" prop="sex">
        <el-radio-group v-model="form.sex">
          <el-radio :label="0">男</el-radio>
          <el-radio :label="1">女</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="年龄:" prop="age">
        <el-input-number v-model="form.age" :min="0"></el-input-number>
      </el-form-item>
      <el-form-item label="体重:" prop="weight">
        <el-input v-model="form.weight" placeholder="请输入患者体重" clearable>
          <template #append>kg</template>
        </el-input>
      </el-form-item>
      <el-form-item label="治疗医生:" prop="doctorName">
        <el-input v-model="form.doctorName" placeholder="请输入治疗医生" clearable></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="close">取消</el-button>
        <el-button type="primary" @click="submit">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, toRaw, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { type Patient } from '@/types/entity';
import { useUserStore } from '@/store/modules/user';
import { debounce } from 'lodash-es'

defineOptions({
  name: 'CreatePatientModal'
})
const emit = defineEmits(['close'])

const visible = ref(false)
const formRef = ref<any>(null)
const snInputRef = ref<any>(null)
const patient = ref<Patient | null>(null)
const title = computed(() => patient.value ? '编辑患者' : '新建患者')
const userStore = useUserStore()

const form = reactive({
  name: '',
  sn: '',
  sex: 0,
  age: 0,
  weight: '',
  doctorName: ''
})

const rules = {
  name: [
    { required: true, message: '请输入患者姓名', trigger: 'blur' },
    {
      validator: (_: any, value: string, callback: any) => {
        const invalidWindowsFileNameChars = /[<>:"/\\|?*\x00-\x1F]/;
        if (invalidWindowsFileNameChars.test(value)) {
          callback(new Error('患者姓名不能包含下列任何字符：\\/:*?"<>|'))
        } else {
          callback()
        }
      }, trigger: 'blur'
    }
  ],
  sn: [
    { required: true, message: '请输入病历编号', trigger: 'blur' },
    {
      validator: (_: any, value: string, callback: any) => {
        const invalidWindowsFileNameChars = /[<>:"/\\|?*\x00-\x1F]/;
        if (invalidWindowsFileNameChars.test(value)) {
          callback(new Error('病历编号不能包含下列任何字符：\\/:*?"<>|'))
        } else {
          callback()
        }
      }, trigger: 'blur'
    }
  ],
  sex: [
    { required: true, message: '请选择性别', trigger: 'change' }
  ],
  age: [
    { required: true, message: '请输入年龄', trigger: 'blur' },
    { type: 'number', message: '年龄必须为数字值', trigger: 'blur' }
  ]
}

const reset = (done: () => void) => {
  form.name = ''
  form.sn = ''
  form.sex = 0
  form.age = 0
  form.weight = ''
  form.doctorName = ''
  formRef.value.resetFields()
  done()
}

const handleInputChange = () => {
  // snInputRef.value.blur()
  let lastKeydownTime = 0
  const KEY_PRESS_THRESHOLD = 50

  const currentTime = Date.now()

  if (currentTime - lastKeydownTime < KEY_PRESS_THRESHOLD) {  // 防抖
    makeInputBlur()
  }
}; // 300ms

// 使snInputRef失去焦点，防抖
const makeInputBlur = debounce(() => {
  snInputRef.value.blur()
}, 50)

const submit = async () => {
  if (!formRef.value) return

  try {
    const valid = await formRef.value.validate()
    console.log('params : ', valid)
    if (!valid) return
    // TODO: 在这里处理表单提交逻辑，例如发送API请求
    let res: any
    if (patient.value) {
      // 编辑患者信息
      res = await window.electronAPI.sqlite.updatePatient({ ...patient.value, ...toRaw(form) } as any)
    } else {
      // 创建新患者信息
      res = await window.electronAPI.sqlite.createPatient({ ...toRaw(form), creatorId: userStore.getUserId } as any)
    }
    console.log('表单提交', form)
    if (res.success) {
      ElMessage.success(res.message || `患者信息${patient.value ? '更新' : '创建'}成功`)
      visible.value = false
      emit('close', { patientId: res?.data?.id })

    } else {
      ElMessage.success(res.message || '患者信息已存在')
    }
  } catch (error) {
    console.error('表单验证失败', error)
    ElMessage.error('请填写所有必填字段')
  }
}

// 暴露方法以便父组件可以打开弹窗
const open = (row?: Patient) => {
  if (row) {
    patient.value = row
    form.age = row.age
    form.name = row.name
    form.sn = row.sn
    form.sex = row.sex
    form.weight = row.weight || ''
    form.doctorName = row.doctorName || ''
  } else {
    patient.value = null
    form.name = ''
    form.sn = ''
    form.sex = 0
    form.age = 0
    form.weight = ''
    form.doctorName = ''
  }
  visible.value = true
}

const close = () => {
  visible.value = false
}

// 暴露方法给父组件
defineExpose({
  open
})
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style>
