import { rmSync } from 'node:fs'
import { resolve } from 'node:path'
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import electron from 'vite-plugin-electron'
import pkg from './package.json' assert { type: 'json' }
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import copy from 'rollup-plugin-copy'
import { viteCommonjs } from '@originjs/vite-plugin-commonjs'
import { viteStaticCopy } from 'vite-plugin-static-copy'

function pathResolve(dir: string) {
  return resolve(process.cwd(), '.', dir)
}

// https://vitejs.dev/config/
export default defineConfig(({ command }) => {
  rmSync('dist-electron', { recursive: true, force: true })

  const isServe = command === 'serve'
  const isBuild = command === 'build'
  const sourcemap = isServe || !!process.env.VSCODE_DEBUG

  return {
    resolve: {
      alias: [
        {
          find: /@\//,
          replacement: pathResolve('src') + '/'
        },
        {
          find: /@db\//,
          replacement: pathResolve('db') + '/'
        }
      ]
    },
    plugins: [
      vue(),
      AutoImport({
        resolvers: [ElementPlusResolver()]
      }),
      Components({
        resolvers: [ElementPlusResolver()]
      }),
      viteCommonjs({
        include: [
          'dicom-parser',
          '@cornerstonejs/codec-libjpeg-turbo-8bit',
          '@cornerstonejs/codec-charls',
          '@cornerstonejs/codec-openjpeg'
        ]
      }),
      viteStaticCopy({
        targets: [
          {
            src: 'node_modules/@cornerstonejs/dicom-image-loader/dist/esm/decodeImageFrameWorker.js',
            dest: 'assets'
          },
          {
            src: 'node_modules/@cornerstonejs/dicom-image-loader/dist/esm/codecs/jpeg.js',
            dest: 'assets'
          }
        ]
      }),
      electron([
        {
          entry: 'electron/main/index.ts',
          onstart(options) {
            options.startup()
          },
          vite: {
            build: {
              sourcemap,
              minify: isBuild,
              outDir: 'dist-electron/main',
              rollupOptions: {
                external: [
                  ...Object.keys('dependencies' in pkg ? pkg.dependencies : {}),
                  'electron/serialport/sqlite.worker.ts'
                ]
              }
            }
          }
        },
        {
          entry: 'electron/preload/index.ts',
          onstart(options) {
            options.reload()
          },
          vite: {
            build: {
              sourcemap,
              minify: isBuild,
              outDir: 'dist-electron/preload',
              rollupOptions: {
                external: Object.keys(
                  'dependencies' in pkg ? pkg.dependencies : {}
                )
              }
            }
          }
        },
        {
          entry: 'electron/db/sqlite.worker.ts',
          vite: {
            build: {
              minify: isBuild,
              outDir: 'dist-electron/main',
              rollupOptions: {
                external: [
                  ...Object.keys('dependencies' in pkg ? pkg.dependencies : {})
                ]
              }
            }
          }
        }
      ])
    ],
    server: process.env.VSCODE_DEBUG
      ? (() => {
          const url = new URL(pkg.debug.env.VITE_DEV_SERVER_URL)
          return {
            host: url.hostname,
            port: +url.port
          }
        })()
      : {
          port: 35173
        },
    clearScreen: false,
    build: {
      rollupOptions: {
        plugins: [
          copy({
            targets: [
              {
                src: [
                  'public/SourceHanSansCN-Bold.otf',
                  'public/SourceHanSansCN-Normal.otf'
                ],
                dest: 'dist/assets/'
              },
              {
                src: 'public/SourceHanSansCN-Bold.otf',
                dest: 'dist/assets/'
              }
            ]
          })
        ]
      }
    },
    worker: {
      format: 'es',
      rollupOptions: {
        external: ['@icr/polyseg-wasm']
      }
    },
    assetsInclude: ['**/*.wasm'],
    optimizeDeps: {
      exclude: ['@cornerstonejs/dicom-image-loader']
      // include: ['dicom-parser'],
    }
  }
})
