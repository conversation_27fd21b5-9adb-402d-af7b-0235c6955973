name: Build

on:
  push:
    branches: [main]
    paths-ignore:
      - "**.md"
      - "**.spec.js"
      - ".idea"
      - ".vscode"
      - ".dockerignore"
      - "Dockerfile"
      - ".gitignore"
      - ".github/**"
      - "!.github/workflows/build.yml"

jobs:
  build:
    runs-on: ${{ matrix.os }}

    strategy:
      matrix:
        os: [macos-latest, ubuntu-latest, windows-latest]

    steps:
      - name: Checkout Code
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 18

      - name: Install Dependencies
        run: npm install

      - name: Build Release Files
        run: npm run build
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Upload Artifact
        uses: actions/upload-artifact@v3
        with:
          name: release_on_${{ matrix. os }}
          path: release/
          retention-days: 5