<script setup lang="ts">
import { ref, watch, reactive, computed } from 'vue'
import InputNumber from '@/components/InputNumber/index.vue'
import { useRouter } from 'vue-router'
import { radiatorNameMap } from '@/views/action/cure/common/radiator'
import { isFourChannel } from '@/utils'

const router = useRouter()

type ISelectOption = {
  id: number
  name: string
  duration: number
  method?: string
  url: string
  [key: string]: unknown
}

const props = defineProps({
  // 设置参数
  realtimePower: {
    type: Number
  },
  power: {
    type: Number
  },
  time: {
    type: String
  },
  countdown: {
    type: String,
    default: '00:00'
  },
  period: {
    type: Number
  },
  dutyRatio: {
    type: Number
  },
  prescriptionId: {
    type: Number
  },
  volume: {
    type: Number
  },
  temperatureOne: {
    type: String
  },
  temperatureTwo: {
    type: String
  },
  temperatureThree: {
    type: String
  },
  temperatureFour: {
    type: String
  },
  temperatureOneSelected: {
    type: Number
  },
  temperatureTwoSelected: {
    type: Number
  },
  temperatureThreeSelected: {
    type: Number
  },
  temperatureFourSelected: {
    type: Number
  },
  radiator: {
    type: Number
  },
  radiatorSelected: {
    type: [Number, Boolean]
  },
  sensorFault: {
    type: Number
  },
  communicationFault: {
    type: Number
  },
  light: {
    type: Boolean,
    default: false
  },

  // 全局状态（双向绑定）
  boot: {
    type: Boolean,
    required: true
  },
  mute: {
    type: Boolean
  },
  lockscreen: {
    type: Boolean
  },
  // 新增全局状态，暂停
  pause: {
    type: Boolean,
    required: true
  },

  // 跳转参数
  type: {
    type: String,
    default: 'pulse'
  },

  title: {
    type: String,
    required: true
  },

  settingTemperature: {
    type: String,
    required: true
  }
})

// const emits = defineEmits(['update:boot', 'update:mute', 'update:lockscreen', 'go-medical-record', 'logout'])
const emits = defineEmits(['update:boot', 'update:pause', 'go-medical-record', 'logout'])

type IChangeType =
  | 'PowerUp'
  | 'PowerDown'
  | 'PeriodUp'
  | 'PeriodDown'
  | 'VolumeUp'
  | 'VolumeDown'
  | 'TimeUp'
  | 'TimeDown'
  | 'DutyRatioUp'
  | 'DutyRatioDown'
  | 'TemperatureUp'
  | 'TemperatureDown'

function handleInputNumberChange(type: IChangeType) {
  console.log('go from page 1 : ', type)
  window.electronAPI.serialport.dispatch(`WavePage.${type}`)
  if (['TimeUp', 'TimeDown'].includes(type) && router.currentRoute.value.query.fromPage) {
    console.log('go from page 2 : ', router.currentRoute.value.query.fromPage, props.lockscreen)
    if (!props.lockscreen) goFromPage()
  }
}

function handleTempSelect(type: 'One' | 'Two' | 'Three' | 'Four') {

  window.electronAPI.serialport.dispatch(`WavePage.ToggleTemperature${type}`)

  if (props.lockscreen) {
    console.log('lock screen')
    switch (type) {
      case 'One':
        parameters.temperatureOneSelected = !parameters.temperatureOneSelected
        break;
      case 'Two':
        parameters.temperatureTwoSelected = !parameters.temperatureTwoSelected
        break;
      case 'Three':
        parameters.temperatureThreeSelected = !parameters.temperatureThreeSelected
        break;
      case 'Four':
        parameters.temperatureFourSelected = !parameters.temperatureFourSelected
        break;
      default:
        break;
    }
  }
}

function handleRadiatorSelect() {
  window.electronAPI.serialport.dispatch(`WavePage.ToggleRadiator`)
  if (props.lockscreen) {
    parameters.radiatorSelected = !parameters.radiatorSelected
  }
  if (props.boot && props.radiator === 0) {
    emits('update:boot', false)
  }
}

// 设置参数
const parameters = reactive({
  temperature: 42,
  temperatureOneSelected: false,
  temperatureTwoSelected: false,
  temperatureThreeSelected: false,
  temperatureFourSelected: false,
  radiatorSelected: false,
  time: 30 as number | string | undefined
})

const radiatorName = computed(() => {
  return props.radiator !== undefined ? (radiatorNameMap[props.radiator] ?? '') : ''
})

watch(() => props.temperatureOneSelected, data => {
  if (data !== undefined) {
    parameters.temperatureOneSelected = !!data
  }
}, {
  immediate: true
})

watch(() => props.temperatureTwoSelected, data => {
  if (data !== undefined) {
    parameters.temperatureTwoSelected = !!data
  }
}, {
  immediate: true
})

watch(() => props.temperatureThreeSelected, data => {
  if (data !== undefined) {
    parameters.temperatureThreeSelected = !!data
  }
}, {
  immediate: true
})

watch(() => props.temperatureFourSelected, data => {
  if (data !== undefined) {
    parameters.temperatureFourSelected = !!data
  }
}, {
  immediate: true
})

watch(() => props.radiatorSelected, data => {
  if (data !== undefined) {
    parameters.radiatorSelected = !!data
  }
})

watch(() => props.countdown, data => {
  if (data !== undefined && props.boot) {
    parameters.time = data
  }
})

watch(() => props.time, data => {
  if (data !== undefined && !props.boot) {
    parameters.time = data
  }
})

watch(() => props.boot, data => {
  if (data !== undefined && !data) {
    parameters.time = props.time
  }
})

function goPage(waveType: string, id: number, time: number) {
  if (waveType === 'pulse') {
    window.electronAPI.serialport.dispatch('WaveformSelection.Pulse')
  }

  if (waveType === 'triangle') {
    window.electronAPI.serialport.dispatch('WaveformSelection.Triangle')
  }

  if (waveType === 'sine') {
    window.electronAPI.serialport.dispatch('WaveformSelection.Sine')
  }

  if (waveType === 'continuation') {
    window.electronAPI.serialport.dispatch('WaveformSelection.Continuation')
  }

  // if (router.currentRoute.value.path.includes(router.currentRoute.value.query.fromPage as string)) {
  //   console.log('go page current : ')
  // } else {
  //   router.push({ path: `/action/cure/${waveType}`, query: { id, time, fromPage: router.currentRoute.value.query.fromPage || props.type } })
  // }
  const queryParams: any = {
    id, time, fromPage: router.currentRoute.value.query.fromPage || props.type
  }
  if (router.currentRoute.value.query.sensorFault || props.sensorFault) {
    queryParams.sensorFault = router.currentRoute.value.query.sensorFault || props.sensorFault
  }
  if (router.currentRoute.value.query.communicationFault || props.communicationFault) {
    queryParams.sensorFault = router.currentRoute.value.query.communicationFault || props.communicationFault
  }
  console.log('router.currentRoute.value.query : ', router.currentRoute.value.query, props.sensorFault)
  router.push({ path: `/action/cure/${waveType}`, query: queryParams })
}

function goFromPage() {
  console.log('router current : ', router.currentRoute.value.path, router.currentRoute.value.query.fromPage)
  // if (router.currentRoute.value.path.includes(router.currentRoute.value.query.fromPage as string)) {
  //   console.log('refresh current : ')
  // } else {
  //   router.push({ path: `/action/cure/${router.currentRoute.value.query.fromPage}` })
  // }
  const fromPage: string = router.currentRoute.value.query.fromPage === 'continuation-realtime-power' ? 'continuation' : router.currentRoute.value.query.fromPage as string
  const queryParams: any = {}
  if (router.currentRoute.value.query.sensorFault || props.sensorFault) {
    queryParams.sensorFault = router.currentRoute.value.query.sensorFault || props.sensorFault
  }
  if (router.currentRoute.value.query.communicationFault || props.communicationFault) {
    queryParams.sensorFault = router.currentRoute.value.query.communicationFault || props.communicationFault
  }
  router.push({ path: `/action/cure/${fromPage}`, query: queryParams })
}

function handlePrescriptionLock() {
  if (props.boot || props.lockscreen) {
    console.log('handle prescription lock')
    window.electronAPI.serialport.dispatch(`WavePage.PrescriptionSelection`)
  }
}

function handlePrescriptionChange(id: number) {
  const option = options.value.find(item => item.id === id)
  console.log('prescription option change : ', option, option?.url, option?.value)
  if (option) {
    window.electronAPI.serialport.dispatch(option.method!)
    if (option.url === 'pulse') {
      window.electronAPI.serialport.dispatch('WaveformSelection.Pulse')
    }

    if (option.url === 'triangle') {
      window.electronAPI.serialport.dispatch('WaveformSelection.Triangle')
    }

    if (option.url === 'sine') {
      window.electronAPI.serialport.dispatch('WaveformSelection.Sine')
    }

    if (option.url === 'continuation') {
      window.electronAPI.serialport.dispatch('WaveformSelection.Continuation')
    }
    goPage(option.url, option.id, option.duration)
  }
}

const options = ref<ISelectOption[]>([
  { id: 1, name: '连续波', duration: 15, method: 'PrescriptionSend.One', url: 'continuation' },
  { id: 2, name: '连续波', duration: 20, method: 'PrescriptionSend.Two', url: 'continuation' },
  { id: 3, name: '连续波', duration: 30, method: 'PrescriptionSend.Three', url: 'continuation' },
  { id: 4, name: '脉冲波', duration: 15, method: 'PrescriptionSend.Four', url: 'pulse' },
  { id: 5, name: '脉冲波', duration: 20, method: 'PrescriptionSend.Five', url: 'pulse' },
  { id: 6, name: '脉冲波', duration: 30, method: 'PrescriptionSend.Six', url: 'pulse' },
  { id: 7, name: '正弦波', duration: 15, method: 'PrescriptionSend.Seven', url: 'sine' },
  { id: 8, name: '正弦波', duration: 20, method: 'PrescriptionSend.Eight', url: 'sine' },
  { id: 9, name: '正弦波', duration: 30, method: 'PrescriptionSend.Nine', url: 'sine' },
  { id: 10, name: '三角波', duration: 15, method: 'PrescriptionSend.Ten', url: 'triangle' },
  { id: 11, name: '三角波', duration: 20, method: 'PrescriptionSend.Eleven', url: 'triangle' },
  { id: 12, name: '三角波', duration: 30, method: 'PrescriptionSend.Twelve', url: 'triangle' },
])

// 启动
function handleBoot() {
  window.electronAPI.serialport.dispatch(`WavePage.Boot`)
  if (props.lockscreen) return
  if (props.radiatorSelected && props.radiator === 0) return
  if (props.sensorFault || props.communicationFault) return
  emits('update:boot', true)

  // 时间显示
}

// 暂停
function handlePause() {
  window.electronAPI.serialport.dispatch(`WavePage.Pause`)
  // TODO: 实现暂定后逻辑
  if (props.lockscreen) return
  emits('update:pause', true)
}


// 停止
function handleStop() {
  window.electronAPI.serialport.dispatch(`WavePage.Stop`)
  if (props.lockscreen) return
  emits('update:pause', false)
  emits('update:boot', false)
}

// 锁屏
function handleLock() {
  window.electronAPI.serialport.dispatch(`WavePage.LockScreen`)

  // lockscreen从下位机取值，暂时删除
  // emits('update:lockscreen', !props.lockscreen)
}

// 静音
function handleMute() {
  window.electronAPI.serialport.dispatch(`WavePage.Mute`)
  if (props.lockscreen) return

  // mute从下位机取值，暂时删除
  // emits('update:mute', !props.mute)
}

defineExpose({
  handleStop
})
</script>

<template>
  <div class="w-[368px] rounded-lg shadow-xl pl-5 pr-[60px] py-6 text-lg left-shadow">
    <el-form :inline="true" label-width="120" class="demo-form-inline">
      <el-form-item v-if="props.type === 'continuation-realtime-power'" label="实时功率：">
        <el-input :model-value="props.realtimePower" readonly class="!w-[150px]" />
        <div class="absolute -right-14 w-8 text-center text-gray-900 text-xl">W</div>
      </el-form-item>
      <el-form-item label="功率：">
        <InputNumber :model-value="props.power" :max="200" :min="1"
          :can-action="props.boot || (!props.boot && props.lockscreen)" @add="handleInputNumberChange('PowerUp')"
          @minus="handleInputNumberChange('PowerDown')" />
        <div class="absolute -right-14 w-8 text-center text-gray-900 text-xl">W</div>
      </el-form-item>
      <el-form-item label="治疗时间：">
        <InputNumber :model-value="props.countdown" :max="120" :min="1"
          :can-action="!props.boot || (props.boot && props.lockscreen)" @add="handleInputNumberChange('TimeUp')"
          @minus="handleInputNumberChange('TimeDown')" />
        <div class="absolute -right-12 w-8 text-center text-gray-900 text-xl">min/s</div>
      </el-form-item>
      <el-form-item label="设定温度：">
        <InputNumber :model-value="props.settingTemperature" :max="45" :min="35" :can-action="true"
          @add="handleInputNumberChange('TemperatureUp')" @minus="handleInputNumberChange('TemperatureDown')" />
        <div class="absolute -right-12 w-8 text-center text-gray-900 text-xl">℃</div>
      </el-form-item>
      <el-form-item v-if="['pulse', 'sine', 'triangle'].includes(props.type)" label="周期：">
        <InputNumber :model-value="props.period" :max="10" :min="1"
          :can-action="!props.boot || (props.boot && props.lockscreen)" @add="handleInputNumberChange('PeriodUp')"
          @minus="handleInputNumberChange('PeriodDown')" />
        <div class="absolute -right-14 w-8 text-center text-gray-900 text-xl">s</div>
      </el-form-item>
      <el-form-item v-if="props.type === 'pulse'" label="占空比：">
        <InputNumber :model-value="props.dutyRatio" :min="1"
          :can-action="!props.boot || (props.boot && props.lockscreen)" @add="handleInputNumberChange('DutyRatioUp')"
          @minus="handleInputNumberChange('DutyRatioDown')" />
        <div class="absolute -right-14 w-8 text-center text-gray-900 text-xl">%</div>
      </el-form-item>
      <el-form-item v-if="false" label="处方选择：">
        <el-select v-model="props.prescriptionId" :disabled="props.boot || props.lockscreen"
          class="w-[150px] prescription-select" @click="handlePrescriptionLock" @change="handlePrescriptionChange">
          <el-option v-for="item in options" :key="item.id" :label="`处方${item.id}：${item.name}（${item?.duration}分钟）`"
            :value="item.id" />
        </el-select>
      </el-form-item>
      <el-divider />
      <el-form-item label="音量：">
        <InputNumber :model-value="props.volume" :max="15" :min="1" :can-action="!props.mute"
          @add="handleInputNumberChange('VolumeUp')" @minus="handleInputNumberChange('VolumeDown')" />
      </el-form-item>
      <el-form-item label="温度T1：">
        <el-input :model-value="props.temperatureOne" readonly class="text-center !w-[150px]" />
        <span class="absolute -right-8 w-8 text-center text-gray-900 text-xl">℃</span>
        <div class="absolute -right-16 w-8 text-center text-gray-900 text-xl">
          <el-checkbox v-model="parameters.temperatureOneSelected" @change="handleTempSelect('One')" />
        </div>
      </el-form-item>
      <el-form-item label="温度T2：">
        <el-input :model-value="props.temperatureTwo" readonly class="text-center !w-[150px]" />
        <span class="absolute -right-8 w-8 text-center text-gray-900 text-xl">℃</span>
        <div class="absolute -right-16 w-8 text-center text-gray-900 text-xl">
          <el-checkbox v-model="parameters.temperatureTwoSelected" @change="handleTempSelect('Two')" />
        </div>
      </el-form-item>
      <el-form-item v-if="isFourChannel" label="温度T3：">
        <el-input :model-value="props.temperatureThree" readonly class="text-center !w-[150px]" />
        <span class="absolute -right-8 w-8 text-center text-gray-900 text-xl">℃</span>
        <div class="absolute -right-16 w-8 text-center text-gray-900 text-xl">
          <el-checkbox v-model="parameters.temperatureThreeSelected" @change="handleTempSelect('Three')" />
        </div>
      </el-form-item>
      <el-form-item v-if="isFourChannel" label="温度T4：">
        <el-input :model-value="props.temperatureFour" readonly class="text-center !w-[150px]" />
        <span class="absolute -right-8 w-8 text-center text-gray-900 text-xl">℃</span>
        <div class="absolute -right-16 w-8 text-center text-gray-900 text-xl">
          <el-checkbox v-model="parameters.temperatureFourSelected" @change="handleTempSelect('Four')" />
        </div>
      </el-form-item>
      <el-form-item label="辐射器：">
        <el-input :model-value="radiatorName" readonly class="!w-[150px]"
          :class="{ 'no-radiator': props.radiator === 0 }" />
        <div class="absolute -right-16 w-8 text-center text-gray-900 text-xl">
          <el-checkbox v-model="parameters.radiatorSelected" @change="handleRadiatorSelect" />
        </div>
      </el-form-item>
      <div class="w-full h-4"></div>
      <div class="flex h-6 leading-6">
        <span class="label">{{ props.title }}：</span>
        <img v-if="props.boot && props.light" src="@/assets/img/light_on.png">
        <img v-else src="@/assets/img/light_off.png">
        <span @click="handleMute">
          <img v-if="props.mute" class="h-full cursor-pointer ml-8" src="@/assets/img/mute.png">
          <img v-else class="h-full cursor-pointer ml-8" src="@/assets/img/vocal.png">
        </span>
      </div>
      <div class="w-full h-4"></div>
      <el-form-item class="w-full">
        <el-col :span="7" :offset="1">
          <el-button type="success" class="w-full" @click="handleBoot">启动</el-button>
        </el-col>
        <el-col :span="7" :offset="1">
          <el-button type="primary" class="w-full" @click="handlePause">暂停</el-button>
        </el-col>
        <el-col :span="7" :offset="1">
          <el-button type="danger" class="w-full" @click="handleStop">停止</el-button>
        </el-col>
      </el-form-item>
      <el-form-item class="w-full">
        <el-col :span="11" :offset="1">
          <el-button type="warning" class="w-full" @click="handleLock">{{ props.lockscreen ? '解锁'
            :
            '锁屏'
            }}</el-button>
        </el-col>
        <el-col :span="11" :offset="1">
          <el-button type="primary" class="w-full" @click="emits('go-medical-record')">病历档案管理</el-button>
        </el-col>
      </el-form-item>
      <el-form-item class="w-full">
        <el-col :span="23" :offset="1">
          <el-button type="primary" class="w-full" @click="emits('logout')">退出</el-button>
        </el-col>
      </el-form-item>
    </el-form>
  </div>
</template>
<style scoped>
:deep(.el-form-item__content) {
  position: relative;
}

:deep(.el-checkbox__inner) {
  width: 20px;
  height: 20px;
}

:deep(.el-form-item__label) {
  font-size: 18px;
}

:deep(.el-checkbox__inner::after) {
  height: 10px;
  width: 5px;
  top: 2px;
  left: 6px;
}

:deep(.el-input__inner) {
  text-align: center;
}

:deep(.prescription-select .el-input__wrapper) {
  padding-left: 35px;
}

:deep(.no-radiator .el-input__inner) {
  color: #F56C6C;
}

.label {
  color: rgb(96, 98, 102);
}

.left-shadow {
  box-shadow: 0px 16px 48px 16px rgb(0 0 0 / 8%), 0px 12px 32px rgb(0 0 0 / 12%), 0px 8px 16px -8px rgb(0 0 0 / 16%);
}

.el-divider--horizontal {
  margin: 4px 0 12px 0;
}
</style>
