<script setup lang="ts">
import { ref, reactive, onMounted, toRaw, watch, onBeforeUnmount } from 'vue'
import PageWrapper from '@/components/PageWrapper/index.vue'
import { useRouter } from 'vue-router'
import type { User } from '@/types/entity';
import { useUserStore } from '@/store/modules/user';

defineOptions({
  name: 'LoginPage'
})

const router = useRouter()
const userStore = useUserStore()

type IBaseInfo = {
  hospitalName: string
  instrumentModel: string
  companyName: string
}

const form = ref<IBaseInfo>({
  instrumentModel: 'N - 6200G 型 微 波 治 疗 仪',
  companyName: '江苏诺万医疗设备有限公司',
  hospitalName: '广西壮族自治区人民医院',
})

onMounted(() => {
  window.electronAPI.sqlite.getBasicInfo().then(res => {
    console.log('basic info res : ', res)
    if (res) {
      form.value.instrumentModel = res.instrumentModel
      form.value.companyName = res.companyName
      form.value.hospitalName = res.hospitalName
    }
  })
})

const loginForm = ref()
const loading = ref(false)

const loginData = reactive({
  loginName: '',
  password: ''
})

const rules = {
  loginName: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度应在3到20个字符之间', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 16, message: '密码长度应在6到16个字符之间', trigger: 'blur' }
  ]
}

const handleSubmit = () => {
  loginForm.value?.validate(async (valid: boolean) => {
    if (valid) {
      loading.value = true
      // 这里应该调用您的登录API
      console.log('登录数据:', loginData)
      const { success, message, user } = await window.electronAPI.sqlite.userLogin(toRaw(loginData))
      loading.value = false
      ElMessage({
        type: success ? 'success' : 'error',
        message
      })
      if (success && user) {
        userStore.setUserData(user)
        router.push({ path: '/home' })
      }
    } else {
      ElMessage.error('请正确填写登录信息')
      return false
    }
  })
}

function handleLogout() {
  console.log('handle logout ~~')
  window.electronAPI.serialport.quitApp()
}

const userOptions = ref<User[]>([])

type IClickState = {
  one: boolean
  two: boolean
  three: boolean
}

const clickState = reactive<IClickState>({ one: false, two: false, three: false })

function onTopRightClick() {
  console.log('右上角点击了：', clickState)
  if (!clickState.one) clickState.one = true
  if (clickState.two) clickState.two = false
  if (clickState.three) clickState.three = false
  console.log('右上角状态变了：', clickState)
}

function onTopLeftClick() {
  console.log('左上角点击了：', clickState)
  if (clickState.one && !clickState.three) clickState.two = true
  console.log('左上角状态变了：', clickState)
}

function onBottomLeftClick() {
  console.log('左下角点击了：', clickState)
  if (clickState.one && clickState.two) clickState.three = true
  console.log('左下角状态变了：', clickState)
}

function goSuperAdminLogin() {
  router.push({ path: '/login/super-admin' })
}

watch(clickState, (newState) => {
  if (newState.one && newState.two && newState.three) {
    goSuperAdminLogin()
  }
})

onBeforeUnmount(() => {
  clickState.one = false
  clickState.two = false
  clickState.three = false
})

onMounted(() => {
  setTimeout(async () => {
    userOptions.value = await window.electronAPI.sqlite.queryUsers({ order: { latestLoginTime: 'DESC' } })
    console.log('user options : ', userOptions.value)
  }, 1000)
})
</script>

<template>
  <PageWrapper show-logo logoutText="退出程序" :logoutAutoBack="false" @logout="handleLogout">
    <div class="content-wrapper pt-12 pb-8 col-flex justify-evenly items-center text-white">
      <h2 class="mb-12 text-5xl font-bold text-black">用户登录</h2>
      <div class="lg:p-8">
        <div class="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]">
          <el-form ref="loginForm" :model="loginData" :rules="rules" size="large" label-position="top"
            @submit.prevent="handleSubmit">
            <el-form-item label="用户名" prop="loginName">
              <el-select v-model="loginData.loginName" filterable clearable placeholder="请选择用户" class="w-full">
                <el-option v-for="user in userOptions" :key="user.id" :label="user.loginName" :value="user.loginName" />
              </el-select>
            </el-form-item>
            <el-form-item label="密码" prop="password">
              <el-input v-model="loginData.password" type="password" placeholder="请输入密码" show-password
                clearable></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" native-type="submit" :loading="loading" class="login-button">
                登录
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
    <div class="absolute w-36 h-36 right-0 top-0" @click="onTopRightClick"></div>
    <div class="absolute w-36 h-36 left-0 top-0" @click="onTopLeftClick"></div>
    <div class="absolute w-36 h-36 left-0 bottom-0 z-50" @click="onBottomLeftClick"></div>
  </PageWrapper>
</template>
<style scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #f0f2f5;
}

.login-card {
  width: 100%;
  max-width: 400px;
}

.login-title {
  text-align: center;
  color: #409EFF;
}

.login-button {
  width: 100%;
  margin-top: 8px;
}
</style>
