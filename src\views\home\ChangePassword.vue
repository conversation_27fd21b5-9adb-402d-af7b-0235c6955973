<template>
  <el-dialog v-model="dialogVisible" title="修改密码" width="30%" :close-on-click-modal="false">
    <el-form ref="formRef" :model="form" :rules="rules" label-width="100px" class="demo-ruleForm">
      <el-form-item label="当前密码" prop="currentPassword">
        <el-input v-model="form.currentPassword" type="password" autocomplete="off" show-password clearable />
      </el-form-item>
      <el-form-item label="新密码" prop="newPassword">
        <el-input v-model="form.newPassword" type="password" autocomplete="off" show-password clearable />
      </el-form-item>
      <el-form-item label="确认新密码" prop="confirmPassword">
        <el-input v-model="form.confirmPassword" type="password" autocomplete="off" show-password clearable />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">
          确认
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/store/modules/user';
import { useRouter } from 'vue-router';

defineOptions({
  name: 'HomeChangePassword'
})

const dialogVisible = ref(false)
const formRef = ref<any>(null)
const userStore = useUserStore()
const router = useRouter()

const form = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

const rules = reactive({
  currentPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' },
    { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    {
      validator: (_: any, value: string, callback: any) => {
        // 长度校验
        if (value.length < 6 || value.length > 16) {
          return callback(new Error("密码长度必须在6到16位之间"))
        }
        const reg = /^[a-zA-Z0-9]+$/
        if (reg.test(value)) {
          callback()
        } else {
          callback(new Error("密码只能包含大写字母、小写字母或数字"))
        }
      },
      trigger: 'blur'
    }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    {
      validator: (_: any, value: string, callback: any) => {
        if (value === form.newPassword) {
          callback()
        } else {
          callback(new Error('两次输入的密码不一致'))
        }
      }, trigger: 'blur'
    }
  ]
})

const handleSubmit = async () => {
  if (!formRef.value) return
  await formRef.value.validate(async (valid: boolean, fields: any[]) => {
    if (valid) {
      console.log('submit!')
      const { success, message } = await window.electronAPI.sqlite.updateUserPassword({ userId: userStore.id!, currentPassword: form.currentPassword, newPassword: form.newPassword })

      if (success) {
        ElMessage.success('密码修改成功，请重新登录！')
        dialogVisible.value = false
        formRef.value.resetFields()
        userStore.logout()
        router.replace('/login')
      } else {
        ElMessage.error(message)
      }
    } else {
      console.log('error submit!', fields)
    }
  })
}

function show() {
  form.confirmPassword = ''
  form.newPassword = ''
  form.currentPassword = ''
  formRef.value?.resetFields?.()
  dialogVisible.value = true
}

defineExpose({ show })
</script>

<style scoped>
.dialog-footer button:first-child {
  margin-right: 10px;
}
</style>
