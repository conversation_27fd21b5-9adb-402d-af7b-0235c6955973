export const PrescriptionSend = {
  One: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x00, 0x01, 0x00, 0x9f],
  Two: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x00, 0x01, 0x00, 0xa0],
  Three: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x00, 0x01, 0x00, 0xa1],
  Four: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x00, 0x01, 0x00, 0xa2],
  Five: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x00, 0x01, 0x00, 0xa3],
  Six: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x00, 0x01, 0x00, 0xa4],
  Seven: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x00, 0x01, 0x00, 0xa5],
  Eight: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x00, 0x01, 0x00, 0xa6],
  Nine: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x00, 0x01, 0x00, 0xa7],
  Ten: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x00, 0x01, 0x00, 0xa8],
  Eleven: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x00, 0x01, 0x00, 0xa9],
  Twelve: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x00, 0x01, 0x00, 0xaa],
  Exit: [0x5a, 0xa5, 0x06, 0x83, 0x10, 0x00, 0x01, 0x00, 0x9e]
}

export type PrescriptionSendMethods =
  | 'PrescriptionSend.One'
  | 'PrescriptionSend.Two'
  | 'PrescriptionSend.Three'
  | 'PrescriptionSend.Four'
  | 'PrescriptionSend.Five'
  | 'PrescriptionSend.Six'
  | 'PrescriptionSend.Seven'
  | 'PrescriptionSend.Eight'
  | 'PrescriptionSend.Nine'
  | 'PrescriptionSend.Ten'
  | 'PrescriptionSend.Eleven'
  | 'PrescriptionSend.Twelve'
