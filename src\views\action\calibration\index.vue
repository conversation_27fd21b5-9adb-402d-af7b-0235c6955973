<script setup lang="ts">
import PageWrapper from '@/components/PageWrapper/index.vue'
import { useRouter } from 'vue-router'
import { onMounted, onUnmounted } from 'vue'
import { isFourChannel } from '@/utils'

defineOptions({
  name: 'CalibrationChoosePage'
})

const router = useRouter()

function goPage(action: string) {
  if (action === 'temperature-one') {
    window.electronAPI.serialport.dispatch('TemperatureCalibrationChooseSend.One')
  }

  if (action === 'temperature-two') {
    window.electronAPI.serialport.dispatch('TemperatureCalibrationChooseSend.Two')
  }

  if (action === 'temperature-three') {
    window.electronAPI.serialport.dispatch('TemperatureCalibrationChooseSend.Three')
  }

  if (action === 'temperature-four') {
    window.electronAPI.serialport.dispatch('TemperatureCalibrationChooseSend.Four')
  }

  setTimeout(() => {
    router.push({ path: `/action/calibration/${action}` })
  }, 300)
}

function handleLogout() {
  setTimeout(() => {
    window.electronAPI.serialport.dispatch('TemperatureCalibrationChooseSend.Exit')
  }, 300)
}

onMounted(() => {
  window.electronAPI.serialport.receive('temperature-calibration-choose-display', (_, { exit }: { exit?: boolean }) => {
    if (exit === true) {
      setTimeout(() => {
        router.back()
      }, 300)
    }
  })
})

onUnmounted(() => {
  window.electronAPI.serialport.removeListener('temperature-calibration-choose-display')
})
</script>

<template>
  <PageWrapper show-logo @logout="handleLogout" :logout-auto-back="false">
    <div class="content-wrapper flex-center flex-col h-[800px]">
      <div class="flex-center">
        <button class="menu-item mr-16" @click="goPage('temperature-one')">T1 校准</button>
        <button class="menu-item" @click="goPage('temperature-two')">T2 校准</button>
      </div>
      <div v-if="isFourChannel" class="flex-center mt-20">
        <button class="menu-item mr-16" @click="goPage('temperature-three')">T3 校准</button>
        <button class="menu-item" @click="goPage('temperature-four')">T4 校准</button>
      </div>
    </div>
  </PageWrapper>
</template>
