import { ref } from 'vue'
import { useNotification } from '@kyvg/vue3-notification'

const notification = useNotification()

export type ToastType = 'success' | 'warning'

export function useToast(type?: ToastType) {
  const toastVisible = ref(false)
  const toastTip = ref('')
  const toastType = ref<ToastType>(type ?? 'success')

  function showToast(tip: string, type: ToastType = 'success') {
    toastTip.value = tip
    toastType.value = type
    toastVisible.value = true

    setTimeout(() => {
      toastVisible.value = false
    }, 3000)
  }

  return {
    toastVisible,
    toastTip,
    toastType,
    showToast
  }
}

var globalNotificationId = 0
export function useWarnNotify(text: string, duration?: number) {
  const id = ++globalNotificationId
  notification.notify({
    group: 'default',
    text,
    type: 'warn',
    duration: duration || -1,
    id: duration ? undefined : id
  })

  return {
    id
  }
}

export function useBottomWarnNotify(
  text: string,
  duration?: number,
  group?: string
) {
  const id = ++globalNotificationId
  notification.notify({
    group: 'bottom',
    text,
    type: 'warn',
    duration: duration || -1,
    id: duration ? undefined : id
  })

  return {
    id
  }
}

export function useSuccessNotify(text: string, duration?: number) {
  const id = ++globalNotificationId
  notification.notify({
    text,
    type: 'success',
    duration: duration || -1,
    id: duration ? undefined : id
  })

  return {
    id
  }
}

export function closeNotify(id: number) {
  notification.notify.close(id)
}

export function closeAllNotify(id: number) {
  notification.notify.close({ group: 'default', clean: true })
  notification.notify.close({ group: 'bottom', clean: true })
}
