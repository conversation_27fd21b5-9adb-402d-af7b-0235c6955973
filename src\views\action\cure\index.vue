<template>
  <PageWrapper :logoutAutoBack="false" show-logo @logout="handleLogout">
    <div class="content-wrapper flex-center">
      <div class="col-flex text-white">
        <button class="menu-item mb-8" @click="goPage('pulse')">脉 冲 波</button>
        <button class="menu-item mb-8" @click="goPage('triangle')">三 角 波</button>
        <button class="menu-item mb-8" @click="goPage('sine')">正 弦 波</button>
        <button class="menu-item" @click="goPage('continuation')">连 续 波</button>
      </div>
    </div>
  </PageWrapper>
</template>

<script setup lang="ts">
import PageWrapper from '@/components/PageWrapper/index.vue'
import { useRouter } from 'vue-router'
import { onMounted, unref, reactive } from 'vue'
import type { SerialportType } from './common/types'
import { useMedicalStore } from '@/store/modules/medical'

const router = useRouter()

// 获取store
const medicalStore = useMedicalStore()

function goPage(waveType: string) {
  console.log('go page path : ', waveType)

  // localStorage.removeItem('cure-params')

  if (waveType === 'pulse') {
    window.electronAPI.serialport.dispatch('WaveformSelection.Pulse')
  }

  if (waveType === 'triangle') {
    window.electronAPI.serialport.dispatch('WaveformSelection.Triangle')
  }

  if (waveType === 'sine') {
    window.electronAPI.serialport.dispatch('WaveformSelection.Sine')
  }

  if (waveType === 'continuation') {
    window.electronAPI.serialport.dispatch('WaveformSelection.Continuation')
  }

  medicalStore.resetFaultState()

  router.push({ path: `/action/cure/${waveType}` })
}

function handleLogout() {
  window.electronAPI.serialport.dispatch('WaveformSelection.Exit')

  router.replace('/action/choose')
}

// 设置参数
const parameters = reactive({
  temperatureOne: '',
  temperatureTwo: ''
})

// 初始化数据接收
function initReceive() {
  // 开启波形界面循环解析
  window.electronAPI.serialport.dispatch('CurrentPage.WavePage')

  // 监听串口解析数据
  window.electronAPI.serialport.receive(
    'wave-page-display',
    (
      _,
      {
        temperatureOne,
        temperatureTwo,
        temperatureOneSelected,
        temperatureTwoSelected,
      }: SerialportType
    ) => {

      if (temperatureOne !== undefined) {
        parameters.temperatureOne =
          temperatureOne.length < 3
            ? temperatureOne.slice(0, 2)
            : temperatureOne.slice(0, 2) + '.' + temperatureOne.slice(2)
      }

      if (temperatureTwo !== undefined) {
        parameters.temperatureTwo =
          temperatureTwo.length < 3
            ? temperatureTwo.slice(0, 2)
            : temperatureTwo.slice(0, 2) + '.' + temperatureTwo.slice(2)
      }
    }
  )
}

onMounted(() => {
  // 开启波形界面循环解析
  initReceive()
})
</script>
